# Use an official Python runtime as a parent image
FROM python:3.11-slim

# Install system dependencies required by OpenCV and potentially other tools
# procps is for `ps` command (debugging), libgomp1 for some ML libraries.
# For Termux, these might be different or installed via `pkg`. This Dockerfile is more for standard Linux.
RUN apt-get update && apt-get install -y --no-install-recommends \
    libgl1-mesa-glx \
    libglib2.0-0 \
    procps \
    libgomp1 \
    && rm -rf /var/lib/apt/lists/*

# Set the working directory in the container
WORKDIR /app

# Copy the dependencies file to the working directory
COPY requirements.txt .

# Install any needed packages specified in requirements.txt
# Using --no-cache-dir to reduce image size
RUN pip install --no-cache-dir -r requirements.txt

# Copy the rest of the application's code to the working directory
# This includes /agents, /src, main_agent_runner.py, .env (if needed for runtime and not just compose)
COPY . .
# Note: .env file is copied here. For Docker builds, it's often better to pass environment
# variables through the `docker run -e` or Docker Compose `environment` section,
# rather than copying the .env file directly into the image, especially if it contains secrets.
# However, since LM_STUDIO_URL is user-specific and might change, copying it makes the
# container rely on the .env file present at runtime if volume mounted, or build-time if not.

# Expose the port the API server will run on
EXPOSE 8000

# Command to run the API server when the container launches
# Uses uvicorn to run the FastAPI application defined in api_server.py, variable 'app'.
# host 0.0.0.0 makes it accessible from outside the container.
CMD ["uvicorn", "api_server:app", "--host", "0.0.0.0", "--port", "8000"]
