import os
from crewai import Agent
from langchain_openai import ChatOpenAI # Using ChatOpenAI to connect to LM Studio
from .custom_tools import run_john_the_ripper
from dotenv import load_dotenv

load_dotenv()

# Configuration for LM Studio
lm_studio_base_url = os.getenv("LM_STUDIO_URL")
lm_studio_api_key = os.getenv("LM_STUDIO_API_KEY", "not-needed")

llm = ChatOpenAI(
    openai_api_base=lm_studio_base_url,
    openai_api_key=lm_studio_api_key,
    model_name="local-model" # Placeholder for model used in LM Studio
)

security_auditor_agent = Agent(
    role='Security Auditor',
    goal='Perform authorized security checks on password hashes using approved tools like John the Ripper, under strict human supervision and explicit instruction.',
    backstory=(
        "A specialized security agent operating under strict protocols. "
        "You ONLY act when given explicit permission for a specific, authorized task involving pre-approved tools. "
        "You do not perform any other actions. All actions must be logged and auditable."
    ),
    tools=[run_john_the_ripper],
    llm=llm,
    allow_delegation=False, # This agent must not delegate security-sensitive tasks
    verbose=True
)

if __name__ == '__main__':
    print("Security Agents module loaded (configured for LM Studio).")
    if not lm_studio_base_url:
        print("Warning: LM_STUDIO_URL is not set. LLM for security agent will not function.")
    else:
        print(f"Attempting to connect to LLM at: {lm_studio_base_url} for security tasks.")
    # Test with extreme caution and only in isolated, authorized environments.
    # from crewai import Task
    # Ensure JTR tool is more than a placeholder and JTR is installed.
    # example_hash_file = "path/to/your/dummy_hashes.txt" # Create this file for testing
    # task = Task(
    #     description=f"Under supervision, run John the Ripper on the hash file '{example_hash_file}'.",
    #     agent=security_auditor_agent,
    #     expected_output="Output from John the Ripper."
    # )
    # result = task.execute() # This would invoke the JTR tool.
    # print(result)
    pass
