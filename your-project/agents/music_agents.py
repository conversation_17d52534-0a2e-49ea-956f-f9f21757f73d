"""
Music Label Operation Agents
Comprehensive agents for running a full-service music label operation including marketing, placements, 
licensing, artist development, legal, radio play, financing, and credit building.
"""

import os
from typing import Dict, List, Any
from crewai import Agent, Task, Crew, Process
from dotenv import load_dotenv
import logging

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import audio tools
try:
    from ..audio_processing import transcribe_audio, understand_audio_context, validate_audio_file
    AUDIO_TOOLS_AVAILABLE = True
except ImportError:
    logger.warning("Audio tools not available. Install required dependencies.")
    AUDIO_TOOLS_AVAILABLE = False

# Import web search and social media tools
try:
    from duckduckgo_search import DDGS
    WEB_SEARCH_AVAILABLE = True
except ImportError:
    WEB_SEARCH_AVAILABLE = False

class MusicLabelAgents:
    """Agents specialized in full-service music label operations"""
    
    @staticmethod
    def create_music_analyst() -> Agent:
        """Agent specialized in analyzing music for licensing opportunities"""
        tools = []
        if AUDIO_TOOLS_AVAILABLE:
            tools.extend([transcribe_audio, understand_audio_context, validate_audio_file])
        
        return Agent(
            role="Music Analyst",
            goal="Analyze music tracks and identify optimal licensing opportunities",
            backstory="""You are an expert in music analysis with deep knowledge of sync licensing.
            You understand how different musical elements appeal to various industries and can
            identify the perfect placement opportunities for any track.""",
            tools=tools,
            verbose=True
        )
    
    @staticmethod
    def create_industry_researcher() -> Agent:
        """Agent specialized in researching sync licensing opportunities"""
        return Agent(
            role="Industry Researcher",
            goal="Identify and research sync licensing opportunities and industry contacts",
            backstory="""You are a specialist in the music licensing industry with extensive
            knowledge of boutique music libraries, production companies, and advertising agencies.
            You know exactly who to contact for different types of music placements.""",
            tools=[],  # Web search and contact finding tools would be added here
            verbose=True
        )
    
    @staticmethod
    def create_licensing_coordinator() -> Agent:
        """Agent specialized in coordinating licensing submissions"""
        return Agent(
            role="Licensing Coordinator",
            goal="Prepare and coordinate music licensing submissions to industry contacts",
            backstory="""You are an expert in music licensing submission processes. You know
            exactly what materials and information are needed for different types of licensing
            opportunities and ensure all submissions are professional and compelling.""",
            tools=[],  # Email and document preparation tools would be added here
            verbose=True
        )
    
    @staticmethod
    def create_marketing_strategist() -> Agent:
        """Agent specialized in music marketing and promotion"""
        return Agent(
            role="Marketing Strategist",
            goal="Develop and execute comprehensive music marketing strategies",
            backstory="""You are a music marketing expert who understands how to promote artists
            and tracks across all channels. You know how to get music placed on playlists,
            featured on blogs, and promoted on social media to maximize exposure.""",
            tools=[],  # Social media, email marketing, and analytics tools
            verbose=True
        )
    
    @staticmethod
    def create_artist_developer() -> Agent:
        """Agent specialized in artist development and career guidance"""
        return Agent(
            role="Artist Developer",
            goal="Guide artists through career development and strategic planning",
            backstory="""You are an experienced artist manager who helps musicians develop their
            sound, brand, and career trajectory. You understand the music industry inside and out
            and can guide artists to success.""",
            tools=[],  # Career planning and development tools
            verbose=True
        )
    
    @staticmethod
    def create_legal_advisor() -> Agent:
        """Agent specialized in music industry legal matters"""
        return Agent(
            role="Legal Advisor",
            goal="Handle all legal aspects of music production and distribution",
            backstory="""You are a music industry lawyer who specializes in contracts, copyrights,
            licensing agreements, and protecting artists' interests. You ensure all business
            dealings are legally sound.""",
            tools=[],  # Legal document review and contract tools
            verbose=True
        )
    
    @staticmethod
    def create_financial_manager() -> Agent:
        """Agent specialized in music business finances"""
        return Agent(
            role="Financial Manager",
            goal="Manage the financial aspects of the music business",
            backstory="""You are a financial expert who understands the unique financial challenges
            of the music industry. You handle budgeting, revenue tracking, investment strategies,
            and credit building for artists and the label.""",
            tools=[],  # Financial analysis and management tools
            verbose=True
        )
    
    @staticmethod
    def create_radio_promoter() -> Agent:
        """Agent specialized in radio promotion and playlist placement"""
        return Agent(
            role="Radio Promoter",
            goal="Secure radio play and playlist placements for tracks",
            backstory="""You are a radio promotion specialist with connections at radio stations
            and streaming platforms. You know how to get tracks added to playlists and secure
            radio play to maximize exposure.""",
            tools=[],  # Radio contact and playlist submission tools
            verbose=True
        )
    
    @staticmethod
    def create_content_creator() -> Agent:
        """Agent specialized in content creation for artists"""
        return Agent(
            role="Content Creator",
            goal="Create compelling content for artists' brands and promotion",
            backstory="""You are a content creation expert who produces engaging material for
            artists across all platforms. You understand how to create content that resonates
            with fans and grows audiences.""",
            tools=[],  # Content creation and editing tools
            verbose=True
        )
    
    @staticmethod
    def create_news_monitor() -> Agent:
        """Agent specialized in monitoring industry news and trends"""
        return Agent(
            role="Industry News Monitor",
            goal="Monitor music industry news and identify opportunities",
            backstory="""You are constantly monitoring music industry news, artist movements,
            label signings, and industry trends. You identify opportunities for our artists
            and alert the team to take action.""",
            tools=[],  # News monitoring and alert tools
            verbose=True
        )
    
    @staticmethod
    def create_contact_scout() -> Agent:
        """Agent specialized in finding and building industry contacts"""
        return Agent(
            role="Contact Scout",
            goal="Find and build relationships with key industry contacts",
            backstory="""You are a networking expert who constantly discovers new contacts in
            the music industry. You research blogs, YouTube channels, playlist curators, and
            industry professionals to build our contact database.""",
            tools=[],  # Contact research and database tools
            verbose=True
        )
    
    @staticmethod
    def create_drip_campaign_manager() -> Agent:
        """Agent specialized in managing drip email campaigns"""
        return Agent(
            role="Drip Campaign Manager",
            goal="Create and manage automated email campaigns for industry contacts",
            backstory="""You are an email marketing expert who creates effective drip campaigns
            to nurture relationships with industry contacts. You understand timing, content,
            and follow-up strategies to convert prospects into opportunities.""",
            tools=[],  # Email marketing and automation tools
            verbose=True
        )

class MusicLabelCrew:
    """Crew for comprehensive music label operations"""
    
    @staticmethod
    def create_full_label_operation_crew(music_file_path: str = None) -> Crew:
        """Create a crew for full music label operations"""
        # Create all agents
        music_analyst = MusicLabelAgents.create_music_analyst()
        industry_researcher = MusicLabelAgents.create_industry_researcher()
        licensing_coordinator = MusicLabelAgents.create_licensing_coordinator()
        marketing_strategist = MusicLabelAgents.create_marketing_strategist()
        artist_developer = MusicLabelAgents.create_artist_developer()
        legal_advisor = MusicLabelAgents.create_legal_advisor()
        financial_manager = MusicLabelAgents.create_financial_manager()
        radio_promoter = MusicLabelAgents.create_radio_promoter()
        content_creator = MusicLabelAgents.create_content_creator()
        news_monitor = MusicLabelAgents.create_news_monitor()
        contact_scout = MusicLabelAgents.create_contact_scout()
        drip_campaign_manager = MusicLabelAgents.create_drip_campaign_manager()
        
        # Define tasks for a comprehensive operation
        tasks = []
        
        # News monitoring task (always run first)
        tasks.append(Task(
            description="""Monitor current music industry news and trends:
            1. Check major music blogs and news sites
            2. Monitor social media for industry movements
            3. Identify new opportunities for artists
            4. Report any breaking news that affects our strategy""",
            agent=news_monitor,
            expected_output="Summary of current industry news and identified opportunities"
        ))
        
        # Contact scouting task
        tasks.append(Task(
            description="""Research and identify new industry contacts:
            1. Search major hip hop blogs and YouTube channels
            2. Identify playlist curators and radio programmers
            3. Find music supervisors and A&R representatives
            4. Build a database of contacts with submission guidelines""",
            agent=contact_scout,
            expected_output="Database of 20+ new industry contacts with submission information"
        ))
        
        # If we have a music file, analyze it
        if music_file_path and AUDIO_TOOLS_AVAILABLE:
            tasks.append(Task(
                description=f"""Analyze the music file at {music_file_path}:
                1. Validate the audio file
                2. Transcribe any vocals or lyrics
                3. Understand the musical context, mood, and genre
                4. Identify key musical elements for marketing and licensing""",
                agent=music_analyst,
                expected_output="Detailed analysis of the music file with marketing and licensing recommendations"
            ))
            
            # Artist development task
            tasks.append(Task(
                description="""Develop the artist's career strategy based on the music analysis:
                1. Identify the artist's unique sound and brand
                2. Develop a career trajectory plan
                3. Recommend target audiences and markets
                4. Create a 12-month development plan""",
                agent=artist_developer,
                expected_output="Comprehensive artist development plan"
            ))
        else:
            # General artist development task
            tasks.append(Task(
                description="""Develop general artist development strategies:
                1. Analyze current market trends
                2. Identify gaps in the market for our artists
                3. Develop branding and positioning strategies
                4. Create artist development frameworks""",
                agent=artist_developer,
                expected_output="General artist development strategies and frameworks"
            ))
        
        # Marketing strategy task
        tasks.append(Task(
            description="""Develop a comprehensive marketing strategy:
            1. Identify target platforms and audiences
            2. Create content calendars and release strategies
            3. Plan social media campaigns and promotions
            4. Develop playlist placement strategies""",
            agent=marketing_strategist,
            expected_output="Detailed marketing strategy with timelines and tactics"
        ))
        
        # Radio promotion task
        tasks.append(Task(
            description="""Plan radio promotion and playlist placement:
            1. Identify target radio stations and streaming playlists
            2. Research key playlist curators and their preferences
            3. Create pitch materials for radio programmers
            4. Develop relationships with key influencers""",
            agent=radio_promoter,
            expected_output="Radio promotion plan with target contacts and pitch materials"
        ))
        
        # Content creation task
        tasks.append(Task(
            description="""Plan content creation for artist promotion:
            1. Develop content themes and messaging
            2. Create content calendars for all platforms
            3. Plan visual content and music videos
            4. Coordinate with marketing strategies""",
            agent=content_creator,
            expected_output="Content creation plan with themes and schedules"
        ))
        
        # Sync licensing research task
        tasks.append(Task(
            description="""Research sync licensing opportunities:
            1. Identify current TV shows, films, and ads seeking music
            2. Research music libraries and production companies
            3. Find music supervisors and their current needs
            4. Compile opportunities with contact information""",
            agent=industry_researcher,
            expected_output="List of 15+ sync licensing opportunities with contact details"
        ))
        
        # Licensing coordination task
        tasks.append(Task(
            description="""Prepare licensing submission materials:
            1. Create compelling track descriptions and metadata
            2. Format contact information and submission guidelines
            3. Prepare professional communication templates
            4. Organize opportunities by priority and fit""",
            agent=licensing_coordinator,
            expected_output="Complete licensing submission package ready for distribution"
        ))
        
        # Legal review task
        tasks.append(Task(
            description="""Review legal aspects of all business dealings:
            1. Examine licensing agreements and contracts
            2. Ensure copyright compliance and protections
            3. Review partnership and distribution agreements
            4. Provide legal guidance on all business decisions""",
            agent=legal_advisor,
            expected_output="Legal review of all business activities with recommendations"
        ))
        
        # Financial management task
        tasks.append(Task(
            description="""Manage the financial aspects of the operation:
            1. Track revenue from all sources
            2. Create budgets for marketing and promotion
            3. Develop investment and credit building strategies
            4. Plan for long-term financial sustainability""",
            agent=financial_manager,
            expected_output="Financial management plan with budgets and strategies"
        ))
        
        # Drip campaign creation task
        tasks.append(Task(
            description="""Create drip email campaigns for industry contacts:
            1. Segment contacts by category and interest
            2. Create email sequences for each segment
            3. Develop compelling content for each touchpoint
            4. Set up automation and tracking systems""",
            agent=drip_campaign_manager,
            expected_output="Drip email campaigns for all contact segments with content and timing"
        ))
        
        return Crew(
            agents=[
                news_monitor, contact_scout, music_analyst, artist_developer,
                marketing_strategist, radio_promoter, content_creator,
                industry_researcher, licensing_coordinator, legal_advisor,
                financial_manager, drip_campaign_manager
            ],
            tasks=tasks,
            process=Process.sequential,
            verbose=True
        )
    
    @staticmethod
    def create_sync_licensing_crew(music_file_path: str = None) -> Crew:
        """Create a crew for sync licensing operations"""
        music_analyst = MusicLabelAgents.create_music_analyst()
        industry_researcher = MusicLabelAgents.create_industry_researcher()
        licensing_coordinator = MusicLabelAgents.create_licensing_coordinator()
        
        tasks = []
        
        # If we have a music file, analyze it first
        if music_file_path and AUDIO_TOOLS_AVAILABLE:
            tasks.append(Task(
                description=f"""Analyze the music file at {music_file_path} to understand its characteristics:
                1. Validate the audio file
                2. Transcribe any vocals or lyrics
                3. Understand the musical context, mood, and genre
                4. Identify key musical elements that would appeal to specific industries""",
                agent=music_analyst,
                expected_output="Detailed analysis of the music file with licensing recommendations"
            ))
            
            tasks.append(Task(
                description="""Based on the music analysis, research and identify:
                1. Boutique music libraries that would be a good fit
                2. Production companies working on relevant projects
                3. Advertising agencies that might need this type of music
                4. Specific contacts at these organizations""",
                agent=industry_researcher,
                expected_output="List of 10-15 licensing opportunities with contact information"
            ))
        else:
            # If no music file, do general industry research
            tasks.append(Task(
                description="""Research current trends in sync licensing and identify:
                1. Top boutique music libraries and their specialties
                2. Production companies actively seeking new music
                3. Advertising agencies with upcoming campaigns
                4. Key contacts and submission guidelines""",
                agent=industry_researcher,
                expected_output="Comprehensive report on current sync licensing opportunities"
            ))
        
        tasks.append(Task(
            description="""Prepare licensing submission materials:
            1. Create compelling descriptions of music tracks
            2. Format contact information and submission guidelines
            3. Prepare professional communication templates
            4. Organize opportunities by priority and fit""",
            agent=licensing_coordinator,
            expected_output="Complete licensing submission package ready for distribution"
        ))
        
        return Crew(
            agents=[music_analyst, industry_researcher, licensing_coordinator],
            tasks=tasks,
            process=Process.sequential,
            verbose=True
        )

# Export classes
__all__ = ['MusicLabelAgents', 'MusicLabelCrew']
