import os
from crewai import Agent
from langchain_openai import Chat<PERSON>penAI # Using ChatOpenAI to connect to LM Studio's OpenAI-compatible endpoint
from .custom_tools import analyze_image
from dotenv import load_dotenv

load_dotenv()

# Configuration for LM Studio
lm_studio_base_url = os.getenv("LM_STUDIO_URL")
lm_studio_api_key = os.getenv("LM_STUDIO_API_KEY", "not-needed")

# For vision capabilities with a local LLM via LM Studio, the model loaded in LM Studio
# MUST be a multimodal model (e.g., LLaVA) and LM Studio must support passing image data.
# The standard ChatOpenAI wrapper might not inherently support multimodal inputs without
# specific formatting that the local server (LM Studio) expects.
# This setup assumes LM Studio's endpoint can handle multimodal requests like OpenAI's API.
# If not, the 'multimodal=True' flag in Agent might not work as expected,
# and analyze_image tool would need to send image data in a way the LLM understands
# (e.g., by first converting image to text description if the LLM is not multimodal,
# or by formatting the input according to how LM Studio expects image data for its multimodal models).

llm = ChatOpenAI(
    openai_api_base=lm_studio_base_url,
    openai_api_key=lm_studio_api_key,
    model_name="local-multimodal-model" # Placeholder: use actual model name if LM Studio uses it
)

vision_analyst_agent = Agent(
  role='Image Analyst',
  goal='Analyze images and provide detailed descriptions and insights. If the image content is visual, describe it. If it contains text, try to extract it.',
  backstory="An expert in computer vision, capable of interpreting visual content. You are currently using a local LLM which may have limitations in directly processing images unless it's a multimodal model.",
  tools=[analyze_image],
  llm=llm,
  # multimodal=True # This flag's effectiveness depends on LM Studio's model and endpoint capabilities.
                  # For now, we rely on the tool to process/describe the image first if direct multimodal is tricky.
  verbose=True,
  allow_delegation=False
)

if __name__ == '__main__':
    print("Vision Agents module loaded (configured for LM Studio).")
    if not lm_studio_base_url:
        print("Warning: LM_STUDIO_URL is not set. LLM/Vision capabilities may not function.")
    else:
        print(f"Attempting to connect to LLM at: {lm_studio_base_url} for vision tasks.")
    # To test, you'd need an image and the ComputerVisionTool to be more than a placeholder,
    # and your LM Studio setup to handle the (potentially text-only) input from the tool.
    pass
