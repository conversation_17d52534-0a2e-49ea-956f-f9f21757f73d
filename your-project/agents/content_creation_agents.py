"""
Content Creation Agents
Specialized agents for creating multimedia content using AI video and image tools.
"""

import os
from typing import Dict, List, Any
from crewai import Agent, Task, Crew, Process
from dotenv import load_dotenv
import logging

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ContentCreationAgents:
    """Agents specialized in AI-powered content creation"""

    @staticmethod
    def create_video_producer() -> Agent:
        """Agent specialized in producing videos using AI tools"""
        return Agent(
            role="AI Video Producer",
            goal="Produce high-quality videos for social media, music videos, and ads using AI video generation tools like Google Veo.",
            backstory="""You are an expert in AI video production, skilled at creating compelling visual content from text prompts and existing footage. 
            You understand the nuances of different video platforms and can tailor content to maximize engagement.""",
            tools=[],  # Placeholder for Google Veo and other video tools
            verbose=True
        )

    @staticmethod
    def create_scriptwriter() -> Agent:
        """Agent specialized in writing scripts for video content"""
        return Agent(
            role="AI Scriptwriter",
            goal="Write engaging scripts for social media posts, music videos, and advertisements.",
            backstory="""You are a creative scriptwriter who excels at crafting narratives that capture attention and drive engagement. 
            You can write in various styles to suit different artists and platforms.""",
            tools=[],
            verbose=True
        )

    @staticmethod
    def create_social_media_manager() -> Agent:
        """Agent specialized in managing social media content"""
        return Agent(
            role="Social Media Manager",
            goal="Manage and schedule social media content across all platforms.",
            backstory="""You are a social media expert who knows the best times to post and how to optimize content for each platform. 
            You ensure a consistent and engaging presence for all artists.""",
            tools=[],  # Placeholder for social media posting tools
            verbose=True
        )

class ContentCreationCrew:
    """Crew for creating and managing multimedia content"""

    @staticmethod
    def create_video_content_crew(artist_name: str, project_brief: str) -> Crew:
        """Create a crew for producing video content for a specific artist and project"""
        video_producer = ContentCreationAgents.create_video_producer()
        scriptwriter = ContentCreationAgents.create_scriptwriter()
        social_media_manager = ContentCreationAgents.create_social_media_manager()

        tasks = [
            Task(
                description=f"Write a script for a {project_brief} for the artist {artist_name}.",
                agent=scriptwriter,
                expected_output="A complete script, including scene descriptions and dialogue/lyrics."
            ),
            Task(
                description=f"Produce a video for the {project_brief} for {artist_name} using the provided script. Utilize AI video generation tools to create the visuals.",
                agent=video_producer,
                expected_output="A link to the final video file, ready for review."
            ),
            Task(
                description=f"Create a social media rollout plan for the {project_brief} video for {artist_name}. Schedule posts across all relevant platforms.",
                agent=social_media_manager,
                expected_output="A schedule of social media posts with captions and hashtags."
            )
        ]

        return Crew(
            agents=[scriptwriter, video_producer, social_media_manager],
            tasks=tasks,
            process=Process.sequential,
            verbose=True
        )

__all__ = ['ContentCreationAgents', 'ContentCreationCrew']
