import os
from crewai import Agent
from langchain_openai import ChatOpenAI
from .custom_tools import read_file
from dotenv import load_dotenv

load_dotenv()

# Configuration for LM Studio (or any OpenAI-compatible local server)
# The user will set LM_STUDIO_URL in their .env file, e.g., http://localhost:1234/v1
# For LM Studio, the API key is often not required or can be a dummy string.
# The 'model' parameter in ChatOpenAI might be ignored by LM Studio if the loaded model is fixed,
# or it might be used to select a model if the server supports it. Adjust as needed.
lm_studio_base_url = os.getenv("LM_STUDIO_URL")
# Use a dummy API key if LM Studio doesn't require one, or let the user set it if needed.
# It's good practice for ChatOpenAI to have an api_key argument.
lm_studio_api_key = os.getenv("LM_STUDIO_API_KEY", "not-needed")

llm = ChatOpenAI(
    openai_api_base=lm_studio_base_url,
    openai_api_key=lm_studio_api_key,
    model_name="local-model" # This might be a placeholder or specific model name for LM Studio
)

# Agent 1: Code Analyst
code_analyst = Agent(
    role="Codebase Analyst",
    goal="Analyze the structure and content of files in the codebase.",
    backstory="An expert in software architecture, skilled at understanding code and identifying areas for improvement.",
    tools=[read_file],
    llm=llm,
    verbose=True,
    allow_delegation=False
)

# Agent 2: Refactoring Specialist
refactor_specialist = Agent(
    role="Code Refactoring Specialist",
    goal="Modify the codebase to add missing features or improve quality, such as adding error handling.",
    backstory="A meticulous developer who specializes in writing clean, robust, and error-free code.",
    tools=[], # This agent will propose changes, not write files directly (for safety)
    llm=llm,
    verbose=True,
    allow_delegation=False
)

if __name__ == '__main__':
    print("Code Analysis Agents module loaded (configured for LM Studio).")
    if not lm_studio_base_url:
        print("Warning: LM_STUDIO_URL is not set in the .env file. LLM will not function.")
    else:
        print(f"Attempting to connect to LLM at: {lm_studio_base_url}")
    # Add test code here if needed, e.g.,
    # from crewai import Task
    # task = Task(description="Analyze a dummy file.", agent=code_analyst, expected_output="A summary")
    # task.execute() # This would require a file and the LM Studio server to be running.
    pass
