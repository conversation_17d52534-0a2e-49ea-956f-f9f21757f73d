#!/usr/bin/env python3
"""
Runner script for the Music Label Operation Crew
"""

import os
import sys
from dotenv import load_dotenv

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from music_agents import MusicLabelCrew

def main():
    """Main function to run the music label operation crew"""
    load_dotenv()
    print("--- Music Label Operation Crew ---")
    
    # Check if a music file was provided as argument
    music_file_path = None
    if len(sys.argv) > 1:
        music_file_path = sys.argv[1]
        if not os.path.exists(music_file_path):
            print(f"Warning: Music file {music_file_path} not found.")
            music_file_path = None
        else:
            print(f"Analyzing music file: {music_file_path}")
    
    print("\n[1] Creating the Full Label Operation Crew...")
    label_crew = MusicLabelCrew.create_full_label_operation_crew(music_file_path)
    
    print("\n[2] Kicking off the crew. This may take several minutes...")
    results = label_crew.kickoff()
    
    print("\n--- Music Label Operation Crew Finished ---")
    print("\n[3] Final Results:")
    print("-" * 50)
    print(results)
    print("-" * 50)
    
    # Save results to file
    with open("music_label_results.txt", "w") as f:
        f.write("Music Label Operation Results\n")
        f.write("=" * 30 + "\n\n")
        f.write(str(results))
    
    print("\nResults saved to music_label_results.txt")
    
    # Also run sync licensing crew for focused licensing work
    print("\n[4] Creating Sync Licensing Crew for focused licensing work...")
    licensing_crew = MusicLabelCrew.create_sync_licensing_crew(music_file_path)
    
    print("\n[5] Kicking off the sync licensing crew...")
    licensing_results = licensing_crew.kickoff()
    
    print("\n--- Sync Licensing Crew Finished ---")
    print("\n[6] Licensing Results:")
    print("-" * 50)
    print(licensing_results)
    print("-" * 50)
    
    # Save licensing results to file
    with open("sync_licensing_results.txt", "w") as f:
        f.write("Sync Licensing Results\n")
        f.write("=" * 20 + "\n\n")
        f.write(str(licensing_results))
    
    print("\nLicensing results saved to sync_licensing_results.txt")

if __name__ == "__main__":
    main()
