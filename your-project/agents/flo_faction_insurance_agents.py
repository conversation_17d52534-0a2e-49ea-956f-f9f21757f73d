#!/usr/bin/env python3
"""
FLO Faction Insurance Agent System
A private, family-owned insurance agency specializing in personal lines including Life, Health, Auto, and Home insurance.
This system automates lead generation from social media and email, client risk assessment,
"""

import os
from typing import Dict, List, Optional, Any
import logging
from crewai import Agent, Task, Crew, Process
# BaseTool is not needed for this implementation
from pydantic import BaseModel, Field
import json
import base64
from email.mime.text import MIMEText
from datetime import datetime
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
import asyncio
from playwright.async_api import async_playwright
from googleapiclient.errors import HttpError

# Import PodGPT for human-like explanations
try:
    from ..language_models import podgpt_client
    PODGPT_AVAILABLE = True
except ImportError:
    PODGPT_AVAILABLE = False
    print("Warning: PodGPT not available. Install required dependencies.")

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Scopes for Gmail API. Using 'modify' to be able to mark emails as read.
GMAIL_SCOPES = ['https://www.googleapis.com/auth/gmail.modify']

# --- Data Models ---

class PolicyHolder(BaseModel):
    name: str
    address: str
    policy_number: str
    coverage_amount: float
    premium: float
    effective_date: str
    expiration_date: str
    flood_zone: str

class Claim(BaseModel):
    claim_id: str
    policy_number: str
    incident_date: str
    damage_description: str
    estimated_loss: float
    status: str = "pending"
    adjuster_notes: str = ""

class Lead(BaseModel):
    source: str = Field(..., description="Where the lead came from (e.g., 'email', 'website', 'social_media')")
    contact_info: str = Field(..., description="Email or phone number of the potential client")
    inquiry_details: str = Field(..., description="The content of the client's message or inquiry")
    status: str = Field("new", description="Status of the lead (e.g., 'new', 'contacted', 'qualified')")

class EmailDraft(BaseModel):
    to_email: str = Field(..., description="The recipient's email address.")
    subject: str = Field(..., description="The subject line of the email.")
    body: str = Field(..., description="The content of the email.")


class FloodRiskAssessment(BaseModel):
    address: str
    flood_zone: str
    base_flood_elevation: float
    annual_flood_probability: float
    risk_level: str
    premium_factor: float

class FLOInsuranceTools:
    """Custom tools for FLO faction insurance operations"""
    
    @staticmethod
    def _get_gmail_service():
        """Authenticates with Google and returns a Gmail service object."""
        creds = None
        # The file token.json stores the user's access and refresh tokens, and is
        # created automatically when the authorization flow completes for the first time.
        if os.path.exists('token.json'):
            creds = Credentials.from_authorized_user_file('token.json', GMAIL_SCOPES)
        # If there are no (valid) credentials available, let the user log in.
        if not creds or not creds.valid:
            if creds and creds.expired and creds.refresh_token:
                creds.refresh(Request())
            else:
                # IMPORTANT: You must have a 'credentials.json' file from Google Cloud Console
                # in your project's root directory for this to work.
                flow = InstalledAppFlow.from_client_secrets_file(
                    'credentials.json', GMAIL_SCOPES)
                creds = flow.run_local_server(port=0)
            # Save the credentials for the next run
            with open('token.json', 'w') as token:
                token.write(creds.to_json())
        
        service = build('gmail', 'v1', credentials=creds)
        logger.info("Gmail service created successfully.")
        return service

    @staticmethod
    def get_internal_flood_risk(address: str) -> Dict[str, Any]:
        logger.info(f"Running proprietary flood risk model for {address}")
        """Get flood risk information for an address using FLO Faction's internal risk model."""
        return {
            "address": address,
            "flood_zone": "FF-3", # Example proprietary zone
            "base_flood_elevation": 12.5, # Still relevant for risk
            "annual_flood_probability": 0.015, # Proprietary calculation
            "risk_level": "Moderate"
        }
    
    @staticmethod
    def calculate_premium(flood_zone: str, coverage_amount: float, building_value: float) -> float:
        logger.info(f"Calculating premium for flood zone {flood_zone} and coverage ${coverage_amount}")
        """Calculate flood insurance premium based on risk factors"""
        zone_factors = {
            "AE": 1.5,
            "VE": 2.0,
            "X": 0.5,
            "A": 1.2,
            "FF-3": 1.3 # Added proprietary zone for accurate calculation
        }
        
        base_rate = 0.002
        zone_factor = zone_factors.get(flood_zone, 1.0)
        risk_factor = (building_value / 100000) * 0.1
        
        premium = coverage_amount * base_rate * zone_factor * (1 + risk_factor)
        return round(premium, 2)
    
    @staticmethod
    def process_internal_claim(claim_data: Dict[str, Any]) -> Dict[str, Any]:
        logger.info(f"Processing internal claim: {claim_data.get('claim_id')}")
        """Processes a claim internally within the FLO Faction system."""
        return {
            "claim_id": claim_data.get('claim_id'),
            "status": "Under Internal Review",
            "processed_date": datetime.now().isoformat(),
            "next_step": "Awaiting damage assessment report."
        }
    
    @staticmethod
    def validate_policy(policy_data: Dict[str, Any]) -> Dict[str, Any]:
        logger.info(f"Validating policy: {policy_data.get('policy_number')}")
        """Validate policy information"""
        errors = []
        
        if not policy_data.get("coverage_amount", 0) > 0:
            errors.append("Coverage amount must be greater than 0")
        
        if not policy_data.get("address"):
            errors.append("Address is required")
        
        if not policy_data.get("policy_number"):
            errors.append("Policy number is required")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors
        }

    @staticmethod
    def scan_emails_for_leads(email_accounts: List[str]) -> List[Dict[str, Any]]:
        """Scans the authenticated Gmail inbox for new insurance inquiries. Use this <NAME_EMAIL>."""
        logger.info(f"Connecting to Gmail API to scan inboxes for: {email_accounts}")
        
        # This tool will scan the inbox of the account authenticated via token.json.
        # We assume <NAME_EMAIL>.
        try:
            service = FLOInsuranceTools._get_gmail_service()
            # Search for unread messages
            results = service.users().messages().list(userId='me', q='is:unread').execute()
            messages = results.get('messages', [])
            
            if not messages:
                logger.info("No new unread emails found.")
                return []

            leads = []
            lead_keywords = ['insurance', 'quote', 'policy', 'flood', 'coverage', 'help', 'inquiry']

            for message_info in messages:
                msg = service.users().messages().get(userId='me', id=message_info['id'], format='full').execute()
                payload = msg.get('payload', {})
                headers = payload.get("headers", [])
                subject = next((header['value'] for header in headers if header['name'].lower() == 'subject'), 'No Subject')
                sender = next((header['value'] for header in headers if header['name'].lower() == 'from'), 'Unknown Sender')

                # Get the email body
                body = ""
                if 'parts' in payload:
                    for part in payload['parts']:
                        if part.get('mimeType') == 'text/plain':
                            data = part.get('body', {}).get('data')
                            if data:
                                body = base64.urlsafe_b64decode(data).decode('utf-8', errors='ignore')
                                break
                else:
                    data = payload.get('body', {}).get('data')
                    if data:
                        body = base64.urlsafe_b64decode(data).decode('utf-8', errors='ignore')
                
                # Simple lead qualification
                if any(keyword in body.lower() or keyword in subject.lower() for keyword in lead_keywords):
                    logger.info(f"Lead found from {sender} with subject: {subject}")
                    lead_data = {
                        "source": "email",
                        "contact_info": sender,
                        "inquiry_details": f"Subject: {subject}\n\nBody:\n{body[:500]}...",
                        "status": "new"
                    }
                    leads.append(Lead(**lead_data).dict())
                
                # Mark email as read by removing the 'UNREAD' label
                service.users().messages().modify(userId='me', id=message_info['id'], body={'removeLabelIds': ['UNREAD']}).execute()
            
            return leads

        except HttpError as error:
            logger.error(f"An error occurred with the Gmail API: {error}")
            return []
        except FileNotFoundError:
            logger.error("Error: 'credentials.json' not found. Please set up Google Cloud credentials for the Gmail API and place the file in the project root.")
            return []
        except Exception as e:
            logger.error(f"An unexpected error occurred in scan_emails_for_leads: {e}")
            return []
    @staticmethod
    def scan_website_for_inquiries(url: str) -> List[Dict[str, Any]]:
        """Scans a website's contact/inquiry form submissions for new leads. Use this for flofaction.com/insurance."""
        logger.info(f"Simulating scanning website for leads at {url}")
        # This would use web scraping tools like BeautifulSoup/Playwright.
        # It's a placeholder for when flofaction.com/insurance is back online.
        if "flofaction.com/insurance" not in url:
            logger.warning(f"Website URL '{url}' does not seem to be the insurance portal. No leads found.")
            return []
        return [
            {
                "source": "website",
                "contact_info": "<EMAIL>",
                "inquiry_details": "Submitted form for insurance quote for 456 Water Way.",
                "status": "new"
            }
        ]

    @staticmethod
    def monitor_social_media(social_media_urls: List[str]) -> List[Dict[str, Any]]:
        """
        Monitors Instagram posts for comments and checks for new Direct Messages (DMs) that are potential leads using Playwright.
        This tool logs into Instagram to scrape comments and DMs.
        Requires INSTAGRAM_USER and INSTAGRAM_PASS environment variables.
        WARNING: Scraping DMs is against Instagram's terms and is very unstable. Use with caution.
        """
        logger.info(f"Starting social media monitoring for URLs and DMs: {social_media_urls}")

        instagram_user = os.getenv("INSTAGRAM_USER")
        instagram_pass = os.getenv("INSTAGRAM_PASS")

        if not instagram_user or not instagram_pass:
            logger.warning("INSTAGRAM_USER or INSTAGRAM_PASS environment variables not set. Skipping social media monitoring.")
            return []

        async def _scrape_instagram():
            leads = []
            lead_keywords = ['quote', 'how much', 'price', 'cost', 'dm me', 'interested', 'need this', 'insurance', 'help']

            async with async_playwright() as p:
                browser = await p.chromium.launch(headless=True) # Set to False for debugging
                context = await browser.new_context(user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36')
                page = await context.new_page()

                try:
                    logger.info("Attempting to log into Instagram...")
                    await page.goto("https://www.instagram.com/accounts/login/")
                    await page.wait_for_selector('input[name="username"]', timeout=15000)
                    await page.fill('input[name="username"]', instagram_user)
                    await page.fill('input[name="password"]', instagram_pass)
                    await page.click('button[type="submit"]')

                    await page.wait_for_selector('svg[aria-label="Home"]', timeout=20000)
                    logger.info("Successfully logged into Instagram.")

                    # --- 1. Scrape Comments from Posts ---
                    for url in social_media_urls:
                        if "instagram.com/p/" in url: # Only scrape comments from specific post URLs
                            logger.info(f"Scraping comments from post: {url}")
                            await page.goto(url)
                            await page.wait_for_timeout(5000) # Allow comments to load

                            # WARNING: Instagram's class names change frequently. This selector is an example.
                            comment_elements = await page.query_selector_all('div._a9zs')
                            for comment_element in comment_elements:
                                comment_text = await comment_element.inner_text()
                                if any(keyword in comment_text.lower() for keyword in lead_keywords):
                                    username_element = await comment_element.query_selector('a')
                                    username = await username_element.inner_text() if username_element else 'unknown_user'
                                    leads.append(Lead(source="instagram_comment", contact_info=f"Username: {username}", inquiry_details=comment_text, status="new_social_lead").dict())
                                    logger.info(f"Found potential lead from comment by {username}: '{comment_text}'")

                    # --- 2. Scrape Direct Messages (DMs) ---
                    logger.info("Navigating to Instagram Direct Messages inbox...")
                    await page.goto("https://www.instagram.com/direct/inbox/")
                    await page.wait_for_timeout(7000)

                    # WARNING: This selector finds unread chats by looking for the blue dot indicator. Highly fragile.
                    unread_chats = await page.query_selector_all("a[href^='/direct/t/']:has(div[style*='background-color: rgb(0, 149, 246)'])")
                    if not unread_chats:
                        logger.info("No unread DMs found.")
                    else:
                        logger.info(f"Found {len(unread_chats)} unread DM conversations.")
                        # It's safer to process one by one by re-navigating to the inbox to avoid stale elements.
                        for i in range(len(unread_chats)):
                            # Re-fetch elements to avoid stale element reference
                            chat_links = await page.query_selector_all("a[href^='/direct/t/']:has(div[style*='background-color: rgb(0, 149, 246)'])")
                            if i >= len(chat_links): break

                            chat_link_element = chat_links[i]
                            # WARNING: This selector for username is also fragile.
                            username = await chat_link_element.query_selector("div[role='none'] span[style*='line-height: 18px;']").inner_text()
                            await chat_link_element.click()
                            await page.wait_for_timeout(5000)

                            message_elements = await page.query_selector_all("div[role='listitem'] div[dir='auto']")
                            if message_elements:
                                last_message_text = await message_elements[-1].inner_text()
                                if any(keyword in last_message_text.lower() for keyword in lead_keywords):
                                    leads.append(Lead(source="instagram_dm", contact_info=f"Username: {username}", inquiry_details=last_message_text, status="new_social_lead").dict())
                                    logger.info(f"Found potential lead in DM from {username}: '{last_message_text}'")

                            await page.goto("https://www.instagram.com/direct/inbox/")
                            await page.wait_for_timeout(5000)

                except Exception as e:
                    logger.error(f"An error occurred during Instagram scraping: {e}. Consider running with headless=False to debug.")
                    await page.screenshot(path='instagram_error.png')
                finally:
                    await browser.close()
            return leads

        # Run the async function within the sync method
        return asyncio.run(_scrape_instagram())

    @staticmethod
    def send_follow_up_email(to_email: str, subject: str, body: str) -> Dict[str, Any]:
        """Sends a follow-up email to a potential lead using the authenticated Gmail account (<EMAIL>)."""
        logger.info(f"Preparing to send email to {to_email} with subject: {subject}")
        try:
            service = FLOInsuranceTools._get_gmail_service()
            message = MIMEText(body)
            message['to'] = to_email
            message['subject'] = subject
            # The 'from' field is automatically set to the authenticated user's email address.
            
            create_message = {'raw': base64.urlsafe_b64encode(message.as_bytes()).decode()}
            
            sent_message = service.users().messages().send(userId="me", body=create_message).execute()
            logger.info(f"Email sent successfully. Message ID: {sent_message['id']}")
            return {"status": "success", "message_id": sent_message['id']}
        except (HttpError, Exception) as e:
            logger.error(f"An error occurred while sending email: {e}")
            return {"status": "error", "details": str(e)}

# FLO Faction Insurance Agents
class FLOInsuranceAgents:
    """Main FLO faction insurance agent system"""
    
    @staticmethod
    def create_policy_analyst() -> Agent:
        """Agent specialized in analyzing and creating flood insurance policies"""
        return Agent(
            role="FLO Policy Analyst",
            goal="Analyze flood risk and create comprehensive insurance policies",
            backstory="""You are a specialized flood insurance analyst with deep knowledge of proprietary
            risk assessment models and underwriting principles for a private insurance firm.""",
            tools=[
                FLOInsuranceTools.get_internal_flood_risk,
                FLOInsuranceTools.calculate_premium,
                FLOInsuranceTools.validate_policy
            ],
            verbose=True
        )
    
    @staticmethod
    def create_claim_adjuster() -> Agent:
        """Agent specialized in processing and adjusting flood insurance claims"""
        return Agent(
            role="FLO Claim Adjuster",
            goal="Process flood insurance claims efficiently and fairly",
            backstory="""You are an experienced flood insurance claim adjuster with expertise in
            damage assessment and internal claim processing for FLO Faction Insurance.""",
            tools=[
                FLOInsuranceTools.process_internal_claim,
                FLOInsuranceTools.get_internal_flood_risk
            ],
            verbose=True
        )
    
    @staticmethod
    def create_risk_assessor() -> Agent:
        """Agent specialized in flood risk assessment and mitigation"""
        return Agent(
            role="FLO Risk Assessor",
            goal="Assess flood risk and recommend mitigation strategies",
            backstory="""You are a flood risk assessment specialist who evaluates properties
            for flood risk using FLO Faction's proprietary models and provides mitigation recommendations.""",
            tools=[
                FLOInsuranceTools.get_internal_flood_risk,
                FLOInsuranceTools.calculate_premium
            ],
            verbose=True
        )

    @staticmethod
    def create_digital_scout() -> Agent:
        """Agent specialized in finding new clients from various digital channels."""
        return Agent(
            role="Digital Scout",
            goal="Proactively find potential clients from various digital channels like email, social media, and web forms by scanning for inquiries.",
            backstory="""You are a digital marketing and sales expert who excels at identifying potential
            customers by monitoring online communications.""",
            tools=[
                FLOInsuranceTools.scan_emails_for_leads,
                FLOInsuranceTools.scan_website_for_inquiries,
                FLOInsuranceTools.monitor_social_media
            ],
            verbose=True,
            allow_delegation=False
        )

    @staticmethod
    def create_engagement_specialist() -> Agent:
        """Agent specialized in crafting and sending communications to new leads."""
        return Agent(
            role="Engagement Specialist",
            goal="Craft and send personalized initial communications to start conversations and gather more information from new leads.",
            backstory="""You are a friendly and professional communication specialist at FLO Faction Insurance.
            Your job is to make the first contact with potential clients, making them feel welcomed and understood, whether through email or by drafting social media replies.""",
            tools=[
                FLOInsuranceTools.send_follow_up_email
            ],
            verbose=True,
            allow_delegation=False
        )
    
    @staticmethod
    def create_client_relations_manager() -> Agent:
        """Agent specialized in human-like client interactions using PodGPT"""
        tools = []
        if PODGPT_AVAILABLE:
            # Import PodGPT tools
            try:
                from ..language_models import explain_like_human, respond_to_client_rebuttal, pitch_product_to_client
                tools.extend([explain_like_human, respond_to_client_rebuttal, pitch_product_to_client])
            except ImportError:
                logger.warning("Could not import PodGPT tools")
        
        return Agent(
            role="Client Relations Manager",
            goal="Interact with clients in a human-like manner, addressing their concerns and explaining insurance products in an accessible way.",
            backstory="""You are an expert in client relations with the ability to explain complex insurance topics in a conversational, human-like manner.
            You specialize in addressing client rebuttals and concerns with empathy and clarity, similar to how topics are explained on science podcasts.""",
            tools=tools,
            verbose=True,
            allow_delegation=False
        )

# Sub-agents for specific functions
class FLOSubAgents:
    """Specialized sub-agents for specific insurance functions"""
    
    @staticmethod
    def create_premium_calculator() -> Agent:
        """Agent specialized in calculating insurance premiums"""
        return Agent(
            role="Premium Calculator",
            goal="Calculate accurate flood insurance premiums",
            backstory="""You are a premium calculation specialist who uses advanced 
            algorithms to determine fair and accurate flood insurance premiums.""",
            tools=[FLOInsuranceTools.calculate_premium],
            verbose=True
        )
    
    @staticmethod
    def create_document_processor() -> Agent:
        """Agent specialized in processing insurance documents"""
        return Agent(
            role="Document Processor",
            goal="Process and validate insurance documents efficiently",
            backstory="""You are a document processing specialist who ensures all 
            insurance documents are complete and compliant.""",
            tools=[FLOInsuranceTools.validate_policy],
            verbose=True
        )
    
    @staticmethod
    def create_damage_assessor() -> Agent:
        """Agent specialized in assessing flood damage"""
        return Agent(
            role="Damage Assessor",
            goal="Accurately assess flood damage for claims",
            backstory="""You are a damage assessment specialist who evaluates flood 
            damage and provides accurate estimates for insurance claims.""",
            tools=[], # This agent's output is a report, not a tool action.
            verbose=True
        )

# Main FLO Insurance Crew
class FLOInsuranceCrew:
    """Main crew for handling FLO faction insurance operations"""
    
    @staticmethod
    def create_policy_creation_crew(policy_data: Dict[str, Any]) -> Crew:
        """Create a crew for policy creation and analysis"""
        policy_analyst = FLOInsuranceAgents.create_policy_analyst()
        risk_assessor = FLOInsuranceAgents.create_risk_assessor()
        premium_calculator = FLOSubAgents.create_premium_calculator()
        
        return Crew(
            agents=[policy_analyst, risk_assessor, premium_calculator],
            tasks=[
                Task(
                    description=f"Analyze flood risk for policy: {policy_data.get('address', 'Unknown')}",
                    agent=risk_assessor,
                    expected_output="Detailed flood risk assessment"
                ),
                Task(
                    description=f"Calculate premium for coverage amount: ${policy_data.get('coverage_amount', 0)}",
                    agent=premium_calculator,
                    expected_output="Accurate premium calculation"
                ),
                Task(
                    description="Create comprehensive policy document",
                    agent=policy_analyst,
                    expected_output="Complete policy document"
                )
            ],
            process=Process.sequential,
            verbose=True
        )
    
    @staticmethod
    def create_claim_processing_crew(claim_data: Dict[str, Any]) -> Crew:
        """Create a crew for claim processing"""
        claim_adjuster = FLOInsuranceAgents.create_claim_adjuster()
        damage_assessor = FLOSubAgents.create_damage_assessor()
        
        return Crew(
            agents=[claim_adjuster, damage_assessor],
            tasks=[
                Task(
                    description=f"Assess damage for claim: {claim_data.get('claim_id', 'Unknown')}",
                    agent=damage_assessor,
                    expected_output="Detailed damage assessment"
                ),
                Task(
                    description="Process claim and determine settlement",
                    agent=claim_adjuster,
                    expected_output="Claim processing results"
                )
            ],
            process=Process.sequential,
            verbose=True
        )
    
    @staticmethod
    def create_lead_generation_crew() -> Crew:
        """
        Creates a crew for finding, qualifying, and engaging with new leads, with a strong focus on social media.
        This crew finds leads, drafts responses, and for emails, sends them. For social media, it prepares replies for human review.
        """
        digital_scout = FLOInsuranceAgents.create_digital_scout()
        policy_analyst = FLOInsuranceAgents.create_policy_analyst()
        engagement_specialist = FLOInsuranceAgents.create_engagement_specialist()

        # Define the social media URLs to monitor. For best results, use URLs of specific, recent posts.
        social_media_urls_to_monitor = [
            "https://www.instagram.com/_flofaction.insurance/", # Main Instagram profile
            "https://www.instagram.com/flofaction.insurance/", # Secondary Instagram profile
            # Add specific post URLs here for comment scraping, e.g., "https://www.instagram.com/p/C61g2a_xJ3e/"
        ]

        return Crew(
            agents=[digital_scout, policy_analyst, engagement_specialist],
            tasks=[
                Task(
                    description=f"""Scan all available digital channels for new leads, with a primary focus on social media.
1. Monitor Instagram for new Direct Messages (DMs) and comments on recent posts that look like leads. Use these URLs for comment scraping: {social_media_urls_to_monitor}.
2. Scan the 'flofaction.insurance' and 'flofaction.business' email inboxes for new inquiries.
3. Check the 'flofaction.com/insurance' website for any form submissions (if it's back online).
Compile all findings into a single list of potential leads.""",
                    agent=digital_scout,
                    expected_output="A list of all potential leads found across social media (comments and DMs), email, and the website, with their source, contact info, and inquiry details."
                ),
                Task(
                    description="""Perform a preliminary analysis on the identified leads to prepare for initial contact.
For each lead, draft a personalized response.
- If the lead is from EMAIL, draft a full email response (subject and body).
- If the lead is from an INSTAGRAM COMMENT, draft a public comment reply. The reply should be friendly, acknowledge their question, and ask them to send a Direct Message (DM) for more details. DO NOT ask for personal information in the public comment.
- If the lead is from an INSTAGRAM DM, draft a private DM reply. The reply should be professional, answer their question if possible, and ask for contact information (like an email address) to provide a formal quote.""",
                    agent=policy_analyst,
                    context={"leads": "The output from the lead scanning task"},
                    expected_output="A structured summary for each lead containing their original details and the drafted response (either an email draft, a public social media comment draft, or a private DM draft)."
                ),
                Task(
                    description="""Execute the engagement plan based on the drafted responses.
- For leads from EMAIL, use the 'send_follow_up_email' tool to send the drafted email.
- For leads from INSTAGRAM (both comments and DMs), present the drafted reply for human review and manual posting. Clearly label these as 'Social Media Reply Draft' and specify if it's for a public comment or a private DM.""",
                    agent=engagement_specialist,
                    expected_output="A confirmation status for each email sent, and a clearly formatted list of social media replies that are ready for a human to copy and paste."
                )
            ],
            process=Process.sequential,
            verbose=True
        )

# Export the main agents and crews
__all__ = [
    'FLOInsuranceAgents',
    'FLOSubAgents',
    'FLOInsuranceCrew',
    'FLOInsuranceTools',
    'PolicyHolder',
    'Claim',
    'Lead',
    'EmailDraft',
    'FloodRiskAssessment',
]
