from crewai.tools import tool

@tool("CodeFileReaderTool")
def read_file(file_path: str) -> str:
    """Reads the content of a specified file from the codebase.
    Assumes 'src' directory is at the same level as 'main_agent_runner.py'.
    """
    try:
        # Correct path assuming this tool is called from main_agent_runner.py at project root
        with open(f"src/{file_path}", "r") as f:
            content = f.read()
        return content
    except FileNotFoundError:
        return f"Error: File not found at src/{file_path}"
    except Exception as e:
        return f"An unexpected error occurred while reading {file_path}: {e}"

@tool("ComputerVisionTool")
def analyze_image(image_path: str) -> str:
    """
    Analyzes an image and returns a description of its basic properties.
    (Initial placeholder - OpenCV specific code will be added in a later step)
    """
    import cv2
    import os

    # Ensure the image path is valid and accessible.
    # If running in Docker, this path needs to be accessible within the container.
    # If main_agent_runner.py is at /app/main_agent_runner.py, and image is at /app/my_image.jpg,
    # then image_path should be 'my_image.jpg'.

    # Construct path relative to the assumed project root (/app in Docker)
    # This assumes the image_path provided is relative to the project root.
    # For example, if an image is in 'your-project/images/test.jpg',
    # the tool should be called with 'images/test.jpg'.

    if not os.path.exists(image_path):
        return f"Error: Image file not found at path: {image_path}"

    try:
        image = cv2.imread(image_path)
        if image is None:
            return f"Error: Could not read image from path: {image_path}. Ensure it is a valid image file and OpenCV can access it."

        height, width, channels = image.shape
        description = f"Image properties: Width={width}px, Height={height}px, Channels={channels}."

        # Basic analysis: e.g., dominant color (very simplified)
        # More complex analysis like object detection would require additional models and libraries.
        # For now, this provides a simple structural description.
        # You could add more OpenCV functions here: e.g., convert to grayscale, detect edges, etc.
        # gray_image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        # edges = cv2.Canny(gray_image, 100, 200)
        # description += f" Edge detection performed (placeholder for actual edge data)."

        # If the LLM is multimodal and can accept image data directly (e.g. base64 encoded),
        # this tool could also return that. For now, it returns a text description.
        # import base64
        # _, buffer = cv2.imencode('.jpg', image)
        # img_str = base64.b64encode(buffer).decode('utf-8')
        # return {"description": description, "image_base64": img_str}

        return description
    except Exception as e:
        return f"Error analyzing image at {image_path}: {e}"

@tool("JohnTheRipperTool")
def run_john_the_ripper(hash_file_path: str) -> str:
    """
    Runs John the Ripper on a specified hash file for authorized security audits.
    Requires explicit user approval. This tool is for security testing ONLY.
    (Initial placeholder - subprocess call will be added in a later step)
    """
    # Security Check:
    print("SECURITY WARNING: Attempting to run John the Ripper. This requires manual approval and setup.")
    # In a real system, you would add a human-in-the-loop approval step here.
    # approval = input("Type 'approve' to continue if JTR is configured and this is an authorized test: ")
    # if approval.lower()!= 'approve':
    #     return "Execution denied by user or not approved."

    import subprocess
    import os

    # Security: Ensure hash_file_path is validated to prevent command injection if part of commands.
    # For this example, it's used as a direct argument to `john`.
    if not os.path.exists(hash_file_path):
        return f"Error: Hash file not found at path: {hash_file_path}"

    # Define a common wordlist path. User might need to change this or make it configurable.
    # This wordlist needs to exist where JtR is run (e.g., inside container or on host if JtR runs there).
    # For Termux, JtR might be installed via `pkg install john`, and wordlists might be in `/usr/share/john/`.
    # For Docker, this file would need to be ADDed or volume mounted.
    wordlist_path = "password.lst" # Example: as per original guide

    # Check if the wordlist exists, provide a more specific error if not.
    # This check is relative to where this script runs.
    # If JTR runs in a different context (e.g. system install in Termux), this check might not be perfectly representative.
    # if not os.path.exists(wordlist_path):
    #     return f"Error: Wordlist '{wordlist_path}' not found. Please ensure it exists or update the path in the tool."

    try:
        # Command to run John the Ripper
        # Ensure 'john' executable is in PATH.
        # The user must have John the Ripper installed on the system running this code (e.g., Termux, or inside Docker).
        # The guide mentions "--rules", which often implies default rules.
        # Using a session name can be good practice for managing multiple JtR runs.
        session_name = f"jtr_session_{os.path.basename(hash_file_path)}"

        print(f"Attempting to run John the Ripper on '{hash_file_path}' with wordlist '{wordlist_path}'. Session: {session_name}")

        command = [
            "john",
            f"--session={session_name}",
            f"--wordlist={wordlist_path}",
            "--rules", # Use default cracking rules
            hash_file_path
        ]

        # Execute John the Ripper to crack hashes
        # Timeout can be added if needed: process.wait(timeout=SECONDS)
        process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        stdout, stderr = process.communicate()

        if process.returncode != 0:
            # Some JtR versions exit with non-0 if no passwords cracked or if it's just starting a session.
            # Inspect stdout/stderr for actual errors.
            # For now, we'll consider any non-zero as a potential issue to report.
            # Common JtR behavior: if it cracks passwords, it might say so and exit 0.
            # If it finishes without cracking any new ones, it might also exit 0.
            # Errors like "No password hashes loaded" are important.
            pass # Continue to check output, as JtR has various exit behaviors.

        # Command to show cracked passwords for the session
        show_command = [
            "john",
            f"--session={session_name}",
            "--show",
            hash_file_path
        ]
        show_process = subprocess.Popen(show_command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        show_stdout, show_stderr = show_process.communicate()

        results = f"John the Ripper Cracking Process Output for {hash_file_path}:\nSTDOUT:\n{stdout}\nSTDERR:\n{stderr}\n\n"
        results += f"John the Ripper Show Cracked Passwords Output:\nSTDOUT:\n{show_stdout}\nSTDERR:\n{show_stderr}\n"

        if show_process.returncode != 0 and not show_stdout: # If show command failed and no output
             results += "Warning: '--show' command might have failed or no passwords cracked yet/found."
        elif not show_stdout.strip() and process.returncode == 0 : # If crack command seemed okay but show is empty
            results += "No passwords cracked or shown for this session yet."

        return results

    except FileNotFoundError:
        return "Error: 'john' command not found. Ensure John the Ripper is installed and in the system's PATH."
    except subprocess.CalledProcessError as e: # Popen doesn't raise this directly, but good to keep for run.
        return f"An error occurred while running John the Ripper: {e.stderr if e.stderr else e.stdout}"
    except Exception as e:
        return f"An unexpected error occurred with JohnTheRipperTool: {e}"
