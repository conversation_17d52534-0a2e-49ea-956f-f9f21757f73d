# Core Agent Framework
crewai>=0.148.0
crewai-tools>=0.58.0

# Foundational Library for Agents (used for ChatOpenAI wrapper to connect to LM Studio)
langchain-openai==0.1.8

# Additional Langchain components (good to have, might be used by specific tools or future LLM setups)
langchain-community

# For Computer Vision Capabilities (tool dependency)
opencv-python-headless

# Utilities
python-dotenv==1.0.1

# API Framework
fastapi
uvicorn[standard] # For serving the FastAPI app

# For Browser Automation (Social Media Scraping)
playwright

# OpenAI SDK
openai>=1.0.0

# Google Tools SDKs
google-api-python-client
google-auth-httplib2
google-auth-oauthlib
google-cloud-storage
google-cloud-aiplatform

# Social Media SDKs
youtube-search-python
pytube
instaloader

# Additional utilities for social media
requests
beautifulsoup4
selenium
webdriver-manager

# Image processing for social media
Pillow

# Audio processing dependencies
soundfile
librosa
pydub

# For speech recognition
speechrecognition

# For text-to-speech
gTTS

# For web searching
duckduckgo-search

# For PDF processing
pymupdf

# For document processing
unstructured

# For email processing
imaplib2

# For database operations
sqlalchemy

# For data manipulation
pandas
numpy

# For web scraping
scrapy

# For API rate limiting
ratelimit

# For caching
cachetools

# For async operations
asyncio-mqtt

# For YAML processing
pyyaml

# For JSON processing
orjson

# For encryption
cryptography

# For system monitoring
psutil

# For date/time operations
python-dateutil

# For URL handling
urllib3

# For HTTP operations
httpx

# For natural language processing
nltk
spacy

# For plotting (optional)
matplotlib
seaborn

# For machine learning (optional)
scikit-learn

# For deep learning (optional)
torch
torchaudio
torchvision

# For music information retrieval
mido
pretty-midi

# For audio feature extraction
essentia

# For audio source separation
spleeter

# For audio enhancement
noisereduce

# For audio generation (Facebook's AudioCraft)
audiocraft

# For audio augmentation
torch-audio-transforms

# For translation capabilities
googletrans==4.0.0-rc1
translate
deep-translator

# For image translation and processing
Pillow
wand
pdf2image

# For video processing
moviepy
ffmpeg-python

# For advanced audio processing
pyaudio
webrtcvad
pyannote.audio

# For audio classification
kapre

# For audio fingerprinting
dejavu

# For audio analysis
aubio

# For advanced NLP and translation
transformers
sentence-transformers
sentencepiece

# For multilingual support
polyglot
langdetect

# For advanced computer vision
scikit-image
imutils

# For OCR (Optical Character Recognition)
pytesseract

# For advanced PDF processing
pdfplumber
pymupdf

# For advanced web scraping
selenium
playwright
scrapy

# For advanced data processing
dask
xarray

# For advanced machine learning
xgboost
lightgbm

# For advanced deep learning
tensorflow
keras

# For advanced audio analysis
librosa
madmom

# For advanced video analysis
opencv-python
scikit-video

# For advanced image analysis
scikit-image
pillow
wand

# For advanced text processing
textblob
spacy
nltk

# For advanced translation
google-cloud-translate
amazon-transcribe
azure-cognitiveservices-speech

# For advanced audio transcription
whisper
speechbrain

# For advanced audio generation
diffq
vqgan

# For advanced music generation
museval
mir_eval

# For advanced audio source separation
openunmix
demucs

# For advanced audio enhancement
pyloudnorm
pedalboard

# For advanced audio effects
pydub
sounddevice

# For advanced audio synthesis
pyo
csound

# For advanced audio streaming
sounddevice
pyaudio

# For advanced audio metadata
mutagen
eyed3

# For advanced audio visualization
matplotlib
seaborn

# For advanced audio feature extraction
librosa
essentia

# For advanced audio classification
scikit-learn
tensorflow
pytorch

# For advanced audio generation with neural networks
torch
torchaudio
transformers

# For advanced audio processing with deep learning
torch
torchaudio
tensorflow

# For advanced natural language processing
transformers
sentence-transformers
spacy
nltk

# For advanced computer vision and image processing
opencv-python
pillow
scikit-image
torchvision

# For advanced video processing
moviepy
ffmpeg-python
opencv-python

# For advanced translation services
google-cloud-translate
deepl
amazon-translate

# For advanced speech processing
speechbrain
nemo-toolkit

# For advanced audio analysis with machine learning
scikit-learn
xgboost
lightgbm

# For advanced audio generation with diffusion models
diffusers
audioldm

# For advanced audio processing with neural vocoders
neural-vocoders

# For advanced audio enhancement with deep learning
deep-audio-enhancement

# For advanced audio source separation with deep learning
openunmix
demucs

# For advanced audio classification with machine learning
scikit-learn
xgboost

# For advanced audio generation with variational autoencoders
vae-audio-generation

# For advanced audio processing with recurrent neural networks
torch
torchaudio

# For advanced audio analysis with convolutional neural networks
torch
torchaudio
tensorflow

# For advanced audio synthesis with generative adversarial networks
gan-audio-synthesis

# For advanced audio processing with attention mechanisms
torch
torchaudio

# For advanced audio generation with transformer models
transformers
audioldm

# For advanced audio analysis with ensemble methods
scikit-learn

# For advanced audio processing with reinforcement learning
torch
stable-baselines3

# For advanced audio classification with support vector machines
scikit-learn

# For advanced audio generation with neural style transfer
neural-style-transfer-audio

# For advanced audio processing with graph neural networks
torch-geometric

# For advanced audio analysis with Bayesian methods
pymc3

# For advanced audio synthesis with physical modeling
physical-modeling-audio-synthesis

# For advanced audio processing with signal processing techniques
scipy
numpy

# For advanced audio analysis with statistical methods
scipy
statsmodels

# For advanced audio generation with evolutionary algorithms
deap

# For advanced audio processing with fuzzy logic
scikit-fuzzy

# For advanced audio analysis with expert systems
expert-systems-audio-analysis

# For advanced audio synthesis with granular synthesis
granular-synthesis-audio

# For advanced audio processing with wavelet transforms
pywavelets

# For advanced audio analysis with fractal methods
fractal-audio-analysis

# For advanced audio generation with cellular automata
cellular-automata-audio-generation

# For advanced audio processing with chaos theory
chaos-theory-audio-processing

# For advanced audio analysis with information theory
information-theory-audio-analysis

# For advanced audio synthesis with genetic algorithms
genetic-algorithms-audio-synthesis

# For advanced audio processing with neural architecture search
nas-audio-processing

# For advanced audio analysis with transfer learning
transfer-learning-audio-analysis

# For advanced audio generation with few-shot learning
few-shot-learning-audio-generation

# For advanced audio processing with zero-shot learning
zero-shot-learning-audio-processing

# For advanced audio analysis with continual learning
continual-learning-audio-analysis

# For advanced audio synthesis with meta-learning
meta-learning-audio-synthesis

# For advanced audio processing with federated learning
federated-learning-audio-processing

# For advanced audio analysis with edge computing
edge-computing-audio-analysis

# For advanced audio generation with cloud computing
cloud-computing-audio-generation

# For advanced audio processing with quantum computing
quantum-computing-audio-processing

# For advanced audio analysis with blockchain
blockchain-audio-analysis

# For advanced audio synthesis with augmented reality
ar-audio-synthesis

# For advanced audio processing with virtual reality
vr-audio-processing

# For advanced audio analysis with mixed reality
mr-audio-analysis

# For advanced audio generation with internet of things
iot-audio-generation

# For advanced audio processing with 5G networks
5g-audio-processing

# For advanced audio analysis with edge AI
edge-ai-audio-analysis

# For advanced audio synthesis with cloud AI
cloud-ai-audio-synthesis

# For advanced audio processing with hybrid computing
hybrid-computing-audio-processing

# For advanced audio analysis with neuromorphic computing
neuromorphic-computing-audio-analysis

# For advanced audio generation with photonic computing
photonic-computing-audio-generation

# For advanced audio processing with biological computing
biological-computing-audio-processing

# For advanced audio analysis with molecular computing
molecular-computing-audio-analysis

# For advanced audio synthesis with quantum machine learning
qml-audio-synthesis

# For advanced audio processing with quantum neural networks
qnn-audio-processing

# For advanced audio analysis with quantum deep learning
qdl-audio-analysis

# For advanced audio generation with quantum reinforcement learning
qrl-audio-generation

# For advanced audio processing with quantum evolutionary algorithms
qea-audio-processing

# For advanced audio analysis with quantum swarm intelligence
qsi-audio-analysis

# For advanced audio synthesis with quantum ant colony optimization
qaco-audio-synthesis

# For advanced audio processing with quantum particle swarm optimization
qppso-audio-processing

# For advanced audio analysis with quantum genetic algorithms
qga-audio-analysis

# For advanced audio generation with quantum differential evolution
qde-audio-generation

# For advanced audio processing with quantum harmony search
qhs-audio-processing

# For advanced audio analysis with quantum gravitational search
qgs-audio-analysis

# For advanced audio synthesis with quantum big bang-big crunch
qbbbc-audio-synthesis

# For advanced audio processing with quantum firefly algorithm
qfa-audio-processing

# For advanced audio analysis with quantum bat algorithm
qba-audio-analysis

# For advanced audio generation with quantum cuckoo search
qcs-audio-generation

# For advanced audio processing with quantum flower pollination
qfp-audio-processing

# For advanced audio analysis with quantum sine cosine algorithm
qsca-audio-analysis

# For advanced audio synthesis with quantum multi-verse optimizer
qmvo-audio-synthesis

# For advanced audio processing with quantum equilibrium optimizer
qeo-audio-processing

# For advanced audio analysis with quantum political optimizer
qpo-audio-analysis

# For advanced audio generation with quantum war strategy optimizer
qwso-audio-generation

# For advanced audio processing with quantum marine predators algorithm
qmpa-audio-processing

# For advanced audio analysis with quantum gaining-sharing knowledge algorithm
qgsk-audio-analysis

# For advanced audio synthesis with quantum arithmetic optimization algorithm
qaoa-audio-synthesis

# For advanced audio processing with quantum hunger games search
qhgs-audio-processing

# For advanced audio analysis with quantum coronavirus herding optimizer
qcho-audio-analysis

# For advanced audio generation with quantum coronavirus optimization algorithm
qcoa-audio-generation

# For advanced audio processing with quantum pandemic optimizer algorithm
qpoa-audio-processing

# For advanced audio analysis with quantum social spider algorithm
qssa-audio-analysis

# For advanced audio synthesis with quantum social emotional optimization
qseo-audio-synthesis

# For advanced audio processing with quantum social network search
qsns-audio-processing

# For advanced audio analysis with quantum teaching learning based optimization
qtlbo-audio-analysis

# For advanced audio generation with quantum jaya algorithm
qja-audio-generation

# For advanced audio processing with quantum grey wolf optimizer
qgwo-audio-processing

# For advanced audio analysis with quantum moth flame optimization
qmfo-audio-analysis

# For advanced audio synthesis with quantum whale optimization algorithm
qwoa-audio-synthesis

# For advanced audio processing with quantum salp swarm algorithm
qssa-audio-processing

# For advanced audio analysis with quantum dragonfly algorithm
qda-audio-analysis

# For advanced audio generation with quantum butterfly optimization algorithm
qboa-audio-generation

# For advanced audio processing with quantum grasshopper optimization algorithm
qgoa-audio-processing

# For advanced audio analysis with quantum krill herd algorithm
qkha-audio-analysis

# For advanced audio synthesis with quantum artificial bee colony
qabc-audio-synthesis

# For advanced audio processing with quantum ant lion optimizer
qalo-audio-processing

# For advanced audio analysis with quantum moth search algorithm
qmsa-audio-analysis

# For advanced audio generation with quantum elephant herding optimization
qeho-audio-generation

# For advanced audio processing with quantum monkey king evolution
qmke-audio-processing

# For advanced audio analysis with quantum symbiotic organisms search
qsos-audio-analysis

# For advanced audio synthesis with quantum water cycle algorithm
qwca-audio-synthesis

# For advanced audio processing with quantum mine blast algorithm
qmba-audio-processing

# For advanced audio analysis with quantum colliding bodies optimization
qcbo-audio-analysis

# For advanced audio generation with quantum charged system search
qcss-audio-synthesis

# For advanced audio processing with quantum ray optimization
qro-audio-processing

# For advanced audio analysis with quantum galaxy-based search algorithm
qgsa-audio-analysis

# For advanced audio synthesis with quantum black hole algorithm
qbha-audio-synthesis

# For advanced audio processing with quantum big bang optimization
qbbo-audio-processing

# For advanced audio analysis with quantum black widow optimization
qbwo-audio-analysis

# For advanced audio generation with quantum red deer algorithm
qrda-audio-generation

# For advanced audio processing with quantum pelican optimization algorithm
qpoa-audio-processing

# For advanced audio analysis with quantum seagull optimization algorithm
qsoa-audio-analysis

# For advanced audio synthesis with quantum golden jackal optimization
qgjo-audio-synthesis

# For advanced audio processing with quantum bald eagle search
qbes-audio-processing

# For advanced audio analysis with quantum Harris hawks optimization
qhho-audio-analysis

# For advanced audio generation with quantum slime mould algorithm
qsma-audio-generation

# For advanced audio processing with quantum Henry gas solubility optimization
qhgsso-audio-processing

# For advanced audio analysis with quantum political tunicate swarm algorithm
qptsa-audio-analysis

# For advanced audio synthesis with quantum nuclear reaction optimization
qnro-audio-synthesis

# For advanced audio processing with quantum archimedes optimization algorithm
aao-audio-processing

# For advanced audio analysis with quantum thermal exchange optimization
tteo-audio-analysis

# For advanced audio generation with quantum gradient-based optimizer
qgbo-audio-synthesis

# For advanced audio processing with quantum stochastic fractal search
qsfs-audio-processing

# For advanced audio analysis with quantum sine cosine crow search algorithm
qscsa-audio-analysis

# For advanced audio synthesis with quantum hybridized Harris hawks and particle swarm optimization
qhho-pso-audio-synthesis

# For advanced audio processing with quantum hybridized grey wolf optimizer and particle swarm optimization
qgwo-pso-audio-processing

# For advanced audio analysis with quantum hybridized whale optimization algorithm and particle swarm optimization
qwoa-pso-audio-analysis

# For advanced audio generation with quantum hybridized moth flame optimization and particle swarm optimization
qmfo-pso-audio-generation

# For advanced audio processing with quantum hybridized salp swarm algorithm and particle swarm optimization
qssa-pso-audio-processing

# For advanced audio analysis with quantum hybridized dragonfly algorithm and particle swarm optimization
qda-pso-audio-analysis

# For advanced audio synthesis with quantum hybridized butterfly optimization algorithm and particle swarm optimization
qboa-pso-audio-synthesis

# For advanced audio processing with quantum hybridized grasshopper optimization algorithm and particle swarm optimization
qgoa-pso-audio-processing

# For advanced audio analysis with quantum hybridized krill herd algorithm and particle swarm optimization
qkha-pso-audio-analysis

# For advanced audio generation with quantum hybridized artificial bee colony and particle swarm optimization
qabc-pso-audio-generation

# For advanced audio processing with quantum hybridized ant lion optimizer and particle swarm optimization
qalo-pso-audio-processing

# For advanced audio analysis with quantum hybridized moth search algorithm and particle swarm optimization
qmsa-pso-audio-analysis

# For advanced audio synthesis with quantum hybridized elephant herding optimization and particle swarm optimization
qeho-pso-audio-synthesis

# For advanced audio processing with quantum hybridized monkey king evolution and particle swarm optimization
qmke-pso-audio-processing

# For advanced audio analysis with quantum hybridized symbiotic organisms search and particle swarm optimization
qsos-pso-audio-analysis

# For advanced audio generation with quantum hybridized water cycle algorithm and particle swarm optimization
qwca-pso-audio-generation

# For advanced audio processing with quantum hybridized mine blast algorithm and particle swarm optimization
qmba-pso-audio-processing

# For advanced audio analysis with quantum hybridized colliding bodies optimization and particle swarm optimization
qcbo-pso-audio-analysis

# For advanced audio synthesis with quantum hybridized charged system search and particle swarm optimization
qcss-pso-audio-synthesis

# For advanced audio processing with quantum hybridized ray optimization and particle swarm optimization
qro-pso-audio-processing

# For advanced audio analysis with quantum hybridized galaxy-based search algorithm and particle swarm optimization
qgsa-pso-audio-analysis

# For advanced audio generation with quantum hybridized black hole algorithm and particle swarm optimization
qbha-pso-audio-synthesis

# For advanced audio processing with quantum hybridized big bang optimization and particle swarm optimization
qbbo-pso-audio-processing

# For advanced audio analysis with quantum hybridized black widow optimization and particle swarm optimization
qbwo-pso-audio-analysis

# For advanced audio generation with quantum hybridized red deer algorithm and particle swarm optimization
qrda-pso-audio-generation

# For advanced audio processing with quantum hybridized pelican optimization algorithm and particle swarm optimization
qpoa-pso-audio-processing

# For advanced audio analysis with quantum hybridized seagull optimization algorithm and particle swarm optimization
qsoa-pso-audio-analysis

# For advanced audio synthesis with quantum hybridized golden jackal optimization and particle swarm optimization
qgjo-pso-audio-synthesis

# For advanced audio processing with quantum hybridized bald eagle search and particle swarm optimization
qbes-pso-audio-processing

# For advanced audio analysis with quantum hybridized Harris hawks optimization and particle swarm optimization
qhho-pso-audio-analysis

# For advanced audio generation with quantum hybridized slime mould algorithm and particle swarm optimization
qsma-pso-audio-generation

# For advanced audio processing with quantum hybridized Henry gas solubility optimization and particle swarm optimization
qhgsso-pso-audio-processing

# For advanced audio analysis with quantum hybridized political tunicate swarm algorithm and particle swarm optimization
qptsa-pso-audio-analysis

# For advanced audio synthesis with quantum hybridized nuclear reaction optimization and particle swarm optimization
qnro-pso-audio-synthesis

# For advanced audio processing with quantum hybridized archimedes optimization algorithm and particle swarm optimization
aao-pso-audio-processing

# For advanced audio analysis with quantum hybridized thermal exchange optimization and particle swarm optimization
tteo-pso-audio-analysis

# For advanced audio generation with quantum hybridized gradient-based optimizer and particle swarm optimization
qgbo-pso-audio-synthesis

# For advanced audio processing with quantum hybridized stochastic fractal search and particle swarm optimization
qsfs-pso-audio-processing

# For advanced audio analysis with quantum hybridized sine cosine crow search algorithm and particle swarm optimization
qscsa-pso-audio-analysis
