#!/bin/bash

echo "### Termux Setup Part 2: Installation and Running Instructions ###"
echo ""
echo "This script provides guidance. You may need to adapt commands based on your Termux setup."
echo "It's recommended to run these commands step-by-step manually to troubleshoot."
echo ""

# --- Prerequisites ---
echo "[*] Updating Termux packages..."
pkg update -y && pkg upgrade -y
echo ""

echo "[*] Installing essential packages: git, python, clang, libjpeg-turbo, opencv, john..."
pkg install git python python-pip clang libjpeg-turbo opencv john -y
# Note: 'john' package in Termux provides <PERSON> the Ripper.
# OpenCV dependencies like libglib2.0-0 are often handled by `pkg install opencv`.
# If `pkg install opencv` has issues, you might need to build from source or find specific Termux packages.
echo ""

# --- LM Studio Setup (Manual - on your PC) ---
echo "---------------------------------------------------------------------------------"
echo "[!] ACTION REQUIRED (on your PC/Laptop where LM Studio will run):"
echo "1. Download and install LM Studio from https://lmstudio.ai/"
echo "2. Open LM Studio, download a model (e.g., Mistral, Llama2, Phi)."
echo "3. Go to the 'Local Server' tab (usually looks like '<->')."
echo "4. Select the model at the top and click 'Start Server'."
echo "5. Note the server address (e.g., http://localhost:1234/v1). If your phone is on the same WiFi,"
echo "   replace 'localhost' with your PC's actual IP address on your local network."
echo "   (You can find your PC's IP address using 'ipconfig' on Windows or 'ifconfig'/'ip addr' on Linux/macOS)."
echo "   Example: If PC IP is *************, the URL will be http://*************:1234/v1"
echo "---------------------------------------------------------------------------------"
echo ""
read -p "Press [Enter] once you have LM Studio running and know its URL..."
echo ""

# --- Project Directory and .env setup ---
PROJECT_DIR="your-project" # Assuming this script is run from outside 'your-project'
# If you ran part 1, 'your-project' directory should exist.
# cd "$PROJECT_DIR" || { echo "Error: Directory '$PROJECT_DIR' not found. Did you run Part 1 script?"; exit 1; }

echo "[*] Setting up .env file..."
echo "The .env file should be in: $PROJECT_DIR/.env"
echo "Please enter the full URL for your LM Studio server (e.g., http://*************:1234/v1)"
read -p "LM Studio URL: " LM_STUDIO_URL_INPUT

# Update .env file
# Check if .env file exists
if [ -f "$PROJECT_DIR/.env" ]; then
    # Use a temporary file for sed changes to avoid issues with in-place editing on some systems
    sed "s|LM_STUDIO_URL=.*|LM_STUDIO_URL=\"$LM_STUDIO_URL_INPUT\"|" "$PROJECT_DIR/.env" > "$PROJECT_DIR/.env.tmp" && mv "$PROJECT_DIR/.env.tmp" "$PROJECT_DIR/.env"
    echo ".env file updated with LM_STUDIO_URL: $LM_STUDIO_URL_INPUT"
else
    echo "Error: $PROJECT_DIR/.env file not found. Please ensure Part 1 script ran correctly."
    echo "You may need to create it manually with the following content:"
    echo "LM_STUDIO_URL=\"$LM_STUDIO_URL_INPUT\""
    echo "LM_STUDIO_API_KEY=\"not-needed\""
    # exit 1
fi
echo ""

# --- Python Virtual Environment and Dependencies ---
echo "[*] Setting up Python virtual environment and installing dependencies..."
cd "$PROJECT_DIR" || { echo "Error: Failed to cd into '$PROJECT_DIR'"; exit 1; }

if [ ! -d "venv" ]; then
    python -m venv venv
    echo "Virtual environment 'venv' created."
else
    echo "Virtual environment 'venv' already exists."
fi

echo "Activating virtual environment..."
source venv/bin/activate
# Your prompt might change to indicate the venv is active.

echo "Installing Python packages from requirements.txt..."
pip install --upgrade pip
pip install -r requirements.txt
# Consider LDFLAGS and CFLAGS for specific packages if pip install fails, e.g., for OpenCV if `pkg install opencv` wasn't enough.
# export LDFLAGS="-L/data/data/com.termux/files/usr/lib"
# export CFLAGS="-I/data/data/com.termux/files/usr/include"
# pip install opencv-python-headless # If needed, but requirements.txt should handle it.

echo "Installing FastAPI and Uvicorn (if not already in requirements.txt, though they should be)..."
pip install fastapi uvicorn[standard]
echo ""

# --- Testing JTR ---
echo "[*] Testing John the Ripper installation..."
if command -v john &> /dev/null; then
    echo "John the Ripper appears to be installed."
    john --test=0 # Run a short self-test
else
    echo "Warning: John the Ripper ('john') command not found. The security agent's JTR tool will fail."
fi
echo "Note: The JTR tool in the project uses a placeholder 'password.lst'. You'll need to create/provide one for actual use."
echo "Example: echo 'password123' > password.lst (in the your-project directory)"
echo ""

# --- Running the Application (Directly in Termux) ---
echo "[*] To run your CrewAI API server in Termux:"
echo "1. Ensure your LM Studio server is RUNNING on your PC and reachable from your phone."
echo "2. Ensure the LM_STUDIO_URL in '$PROJECT_DIR/.env' is correct (points to your PC's IP and LM Studio port)."
echo "3. Activate the virtual environment: source $PROJECT_DIR/venv/bin/activate"
echo "4. Navigate to the project directory: cd $PROJECT_DIR"
echo "5. Run the API server: uvicorn api_server:app --host 0.0.0.0 --port 8000 --reload"
echo "   (The --reload flag is for development, you can remove it for more stable runs)"
echo "   The API will be accessible at http://<your_phone_ip>:8000 from your PC's browser or tools like Postman/curl."
echo "   You can find your phone's IP in WiFi settings. FastAPI docs at http://<your_phone_ip>:8000/docs"
echo ""
echo "[*] To run the old CLI runner (main_agent_runner.py) for direct testing (optional):"
echo "1. Follow steps 1-4 above."
echo "2. Run: python main_agent_runner.py"
echo "   (Ensure you've uncommented test code within that script if you want it to do something)."
echo ""

# --- Docker (Experimental on Termux) ---
echo "[!] Docker on Termux is EXPERIMENTAL and ADVANCED. These steps are for guidance if you attempt it."
echo "   It's often easier to run the API server directly in Termux first."
echo ""
echo "   To attempt Docker (which will run the api_server.py by default as per Dockerfile CMD):"
echo "   1. Install Docker in Termux (complex, search for 'Termux Docker setup guides')."
echo "      This might involve setting up a proot Linux environment (e.g., Alpine or Ubuntu) within Termux."
echo "   2. Once Docker is running in Termux, navigate to '$PROJECT_DIR'."
echo "   3. Ensure LM_STUDIO_URL in .env is correct for Docker container access (might need PC's LAN IP)."
echo "   4. Build the Docker image: docker compose build"
echo "   5. Run the container: docker compose up"
# The compose.yaml is set to expose port 8000.
echo "   Networking between Termux Docker container and PC's LM Studio can be tricky."
echo "   You might need to use your PC's LAN IP in LM_STUDIO_URL and potentially use 'network_mode: host' in compose.yaml (with caution)."
echo ""

echo "### Setup Guidance Complete ###"
echo "Remember to activate the virtual environment (source venv/bin/activate) in new sessions before running Python scripts."
cd .. # Go back to original directory
# To deactivate venv: deactivate
```
