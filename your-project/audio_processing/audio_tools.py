"""
Audio Processing Tools
Custom tools for audio processing that can be used by AI agents
"""

from crewai.tools import tool
from typing import Dict, Any
import os
from .audio_client import AudioProcessingClient

# Initialize the audio processing client
audio_client = AudioProcessingClient()

@tool("AudioTranscriptionTool")
def transcribe_audio(audio_file_path: str, model: str = "whisper") -> str:
    """
    Transcribe an audio file to text using the specified model.
    
    Args:
        audio_file_path (str): Path to the audio file to transcribe
        model (str): Model to use for transcription (whisper, audio_flamingo, mistral_audio)
        
    Returns:
        str: Transcribed text from the audio file
    """
    try:
        if not os.path.exists(audio_file_path):
            return f"Error: Audio file not found at {audio_file_path}"
        
        transcription = audio_client.transcribe_audio(audio_file_path, model)
        return transcription
    except Exception as e:
        return f"Error transcribing audio: {str(e)}"

@tool("AudioContextUnderstandingTool")
def understand_audio_context(audio_file_path: str, model: str = "audio_flamingo") -> str:
    """
    Understand the context of an audio file, including ambient sounds and speaker emotions.
    
    Args:
        audio_file_path (str): Path to the audio file to analyze
        model (str): Model to use for understanding (audio_flamingo, mistral_audio)
        
    Returns:
        str: Detailed understanding of the audio context
    """
    try:
        if not os.path.exists(audio_file_path):
            return f"Error: Audio file not found at {audio_file_path}"
        
        context = audio_client.understand_audio_context(audio_file_path, model)
        
        # Format the context as a readable string
        result = f"Audio Context Analysis:\n"
        result += f"Transcription: {context.get('transcription', 'N/A')}\n"
        result += f"Context: {context.get('context', 'N/A')}\n"
        result += f"Ambient Sounds: {', '.join(context.get('ambient_sounds', []))}\n"
        result += f"Speaker Emotions: {', '.join(context.get('speaker_emotions', []))}\n"
        
        if 'speaker_count' in context:
            result += f"Speaker Count: {context['speaker_count']}\n"
            
        if 'error' in context:
            result += f"Error: {context['error']}\n"
            
        return result
    except Exception as e:
        return f"Error understanding audio context: {str(e)}"

@tool("AudioFileValidatorTool")
def validate_audio_file(audio_file_path: str) -> str:
    """
    Validate an audio file and provide information about it.
    
    Args:
        audio_file_path (str): Path to the audio file to validate
        
    Returns:
        str: Information about the audio file
    """
    try:
        if not os.path.exists(audio_file_path):
            return f"Error: Audio file not found at {audio_file_path}"
        
        # Get file information
        file_size = os.path.getsize(audio_file_path)
        file_extension = os.path.splitext(audio_file_path)[1]
        
        # Try to get audio properties using soundfile if available
        try:
            import soundfile as sf
            info = sf.info(audio_file_path)
            duration = info.duration
            sample_rate = info.samplerate
            channels = info.channels
            
            result = f"Audio File Information:\n"
            result += f"File Path: {audio_file_path}\n"
            result += f"File Size: {file_size} bytes\n"
            result += f"File Extension: {file_extension}\n"
            result += f"Duration: {duration:.2f} seconds\n"
            result += f"Sample Rate: {sample_rate} Hz\n"
            result += f"Channels: {channels}\n"
            
            return result
        except ImportError:
            # Fallback if soundfile is not available
            result = f"Audio File Information:\n"
            result += f"File Path: {audio_file_path}\n"
            result += f"File Size: {file_size} bytes\n"
            result += f"File Extension: {file_extension}\n"
            result += f"Note: Install 'soundfile' for detailed audio properties\n"
            
            return result
    except Exception as e:
        return f"Error validating audio file: {str(e)}"
