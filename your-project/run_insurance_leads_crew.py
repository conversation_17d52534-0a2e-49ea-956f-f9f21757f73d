#!/usr/bin/env python3
"""
Test runner for the FLO Faction Insurance Lead Generation Crew.

This script initializes and runs the lead generation crew to find, qualify,
and engage with new leads from social media and email.

Prerequisites:
1. A 'credentials.json' file for the Gmail API in the project root.
2. A 'token.json' file will be created on the first run after authentication.
3. An '.env' file in the project root with:
   - INSTAGRAM_USER="your_instagram_username"
   - INSTAGRAM_PASS="your_instagram_password"
"""

import os
from dotenv import load_dotenv
from agents.flo_faction_insurance_agents import FLOInsuranceCrew

def main():
    """
    Main function to set up and run the lead generation crew.
    """
    # Load environment variables from .env file
    load_dotenv()
    print("--- FLO Faction Insurance Lead Generation Test ---")

    # Check for necessary environment variables
    if not os.getenv("INSTAGRAM_USER") or not os.getenv("INSTAGRAM_PASS"):
        print("\nERROR: INSTAGRAM_USER and INSTAGRAM_PASS must be set in your .env file.")
        print("The crew will run, but social media monitoring will be skipped.")
    
    # Check for Gmail credentials
    if not os.path.exists('credentials.json'):
        print("\nERROR: 'credentials.json' not found.")
        print("Please set up Google Cloud credentials for the Gmail API.")
        print("The crew will run, but email scanning will fail.")

    print("\n[1] Creating the Lead Generation Crew...")
    lead_crew = FLOInsuranceCrew.create_lead_generation_crew()

    print("\n[2] Kicking off the crew. This may take a few minutes...")
    print("The system will now attempt to log into Instagram, scan for comments, check emails, and draft responses.")
    
    results = lead_crew.kickoff()

    print("\n--- Lead Generation Crew Finished ---")
    print("\n[3] Final Results:")
    print("---------------------------------------")
    print(results)
    print("---------------------------------------")
    print("\nReview the output above for drafted social media replies and email sending confirmations.")

if __name__ == "__main__":
    main()