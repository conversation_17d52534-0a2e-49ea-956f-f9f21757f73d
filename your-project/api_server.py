from fastapi import FastAP<PERSON>, HTTPException
from pydantic import BaseModel
import os
from dotenv import load_dotenv

# Load environment variables from .env file
# This is important for accessing LM_STUDIO_URL if your agents need it directly,
# or any other configuration you might store in .env
load_dotenv()

# Import your agents and crew setup logic
# Adjust these imports based on where your agent/crew definitions are
# For now, let's assume we might import specific agents or a function to run a crew
from agents.code_analysis_agents import code_analyst
# from agents.vision_agents import vision_analyst_agent # Uncomment as needed
# from agents.security_agents import security_auditor_agent # Uncomment as needed
# from main_agent_runner import run_crew # If main_agent_runner contains a reusable function

from crewai import Crew, Process, Task

# Initialize FastAPI app
app = FastAPI(
    title="CrewAI Agent System API",
    description="An API to interact with CrewAI agents and tasks.",
    version="0.1.0"
)

# --- Pydantic Models for Request and Response ---
class TaskRequest(BaseModel):
    task_description: str
    agent_role: str # To specify which agent (e.g., "Codebase Analyst") or a general role
    # Add other parameters like specific file paths, image urls etc. as needed
    # For example: file_path: Optional[str] = None

class TaskResponse(BaseModel):
    task_id: str # Or some identifier if we make it async later
    status: str
    result: str # The output from the crew/task execution
    # error_message: Optional[str] = None

class AgentInfo(BaseModel):
    role: str
    goal: str
    # backstory: str # Optional
    # tools: list[str] # Optional, list of tool names

class ToolInfo(BaseModel):
    name: str
    description: str


# --- In-memory storage (temporary, for simplicity) ---
# For a production system, you'd use a database or a more robust task management system.
# tasks_db = {} # For async tasks later

# --- API Endpoints ---

@app.get("/")
async def read_root():
    return {"message": "Welcome to the CrewAI Agent System API. Visit /docs for API documentation."}

@app.get("/agents/", response_model=list[AgentInfo])
async def list_agents():
    """Lists all registered agents and their details."""
    agent_infos = []
    for role, agent_instance in AGENT_REGISTRY.items():
        # Ensure agent_instance has 'goal' attribute. CrewAI agents do.
        goal = getattr(agent_instance, 'goal', 'N/A')
        agent_infos.append(AgentInfo(role=role, goal=goal))
    if not agent_infos:
        # This case should ideally not happen if AGENT_REGISTRY is populated.
        return [AgentInfo(role="No agents registered", goal="Please check server configuration.")]
    return agent_infos

@app.get("/tools/", response_model=list[ToolInfo])
async def list_tools():
    """
    Lists all available custom tools by inspecting the agents.custom_tools module.
    This is a basic implementation; a more robust solution might involve a tool registry
    or decorators that automatically register tools.
    """
    tool_infos = []
    # Inspect the custom_tools module for functions decorated with @tool
    # This is a simplified way. A better way would be to have a tool registry
    # or for agents to list their tools.
    import inspect
    from agents import custom_tools # Ensure this import is correct
    from crewai_tools.tools_manager import ToolsManager # To check if it's a BaseTool

    for name, member in inspect.getmembers(custom_tools):
        # Check if it's a function and has the '_crew_tool' attribute added by @tool decorator
        # or if it's an instance of a class that might be a tool (e.g. inherits from BaseTool)
        is_crew_tool_decorator = hasattr(member, '_crew_tool')

        # A more robust check might be needed if tools are classes, not just decorated functions
        # For now, focusing on @tool decorated functions from crewai_tools.tool
        if is_crew_tool_decorator:
            description = inspect.getdoc(member) or "No description available."
            # The actual name used by CrewAI is often the function name or specified in @tool("ActualName")
            # The @tool decorator stores the name in member.name
            tool_name = getattr(member, 'name', name)
            tool_infos.append(ToolInfo(name=tool_name, description=description.strip()))

    if not tool_infos:
        return [ToolInfo(name="No custom tools found", description="Check agents/custom_tools.py")]
    return tool_infos



# --- In-memory store for task results (for synchronous tasks, this is very basic) ---
# This will only store the result of the *last* successfully completed synchronous task.
# For a real system with async tasks or multiple users, a proper database/task queue is needed.
LAST_TASK_RESULT_STORE = {
    "task_id": None,
    "status": None,
    "result": None,
    "error_message": None
}

@app.post("/tasks/", response_model=TaskResponse)
async def submit_task(task_request: TaskRequest):
    """
    Submits a task to a specified agent.
    Currently runs synchronously.
    """
    print(f"Received task request: {task_request.task_description} for agent role: {task_request.agent_role}")

    agent_to_use = AGENT_REGISTRY.get(task_request.agent_role)

    if not agent_to_use:
        print(f"Error: Agent with role '{task_request.agent_role}' not found.")
        raise HTTPException(status_code=404, detail=f"Agent with role '{task_request.agent_role}' not found.")

    # Ensure the agent's LLM is configured (it should be if agents are imported correctly and .env is loaded)
    if not agent_to_use.llm:
        print("Error: Selected agent's LLM is not configured.")
        raise HTTPException(status_code=500, detail="Selected agent's LLM is not configured. Check LM_STUDIO_URL.")

    # Create a CrewAI Task
    try:
        crew_task = Task(
            description=task_request.task_description,
            agent=agent_to_use,
            expected_output="The result of the agent's execution based on the task description." # Generic expected output
        )

        # Create a simple Crew with just this one agent and task for synchronous execution
        # For multiple agents or complex workflows, this setup would be more involved.
        task_crew = Crew(
            agents=[agent_to_use], # Only the selected agent
            tasks=[crew_task],
            process=Process.sequential, # Sequential for a single task
            verbose=1 # Or 2 for more detail, 0 for less. Can be made configurable.
        )

        print(f"Kicking off crew for task: {crew_task.description}")
        # This is a synchronous call. The API will wait for the result.
        task_result = task_crew.kickoff()

        print(f"Task completed. Result: {task_result}")

        # Store result of this synchronous task
        task_id = "sync_task_001" # Using a static ID for now
        LAST_TASK_RESULT_STORE["task_id"] = task_id
        LAST_TASK_RESULT_STORE["status"] = "completed"
        LAST_TASK_RESULT_STORE["result"] = str(task_result)
        LAST_TASK_RESULT_STORE["error_message"] = None

        return TaskResponse(
            task_id=task_id,
            status="completed",
            result=str(task_result)
        )

    except HTTPException as http_exc: # Re-raise HTTPException
        LAST_TASK_RESULT_STORE["task_id"] = "sync_task_failed"
        LAST_TASK_RESULT_STORE["status"] = "error"
        LAST_TASK_RESULT_STORE["result"] = None
        LAST_TASK_RESULT_STORE["error_message"] = http_exc.detail
        raise http_exc
    except Exception as e:
        # Store error info
        LAST_TASK_RESULT_STORE["task_id"] = "sync_task_failed"
        LAST_TASK_RESULT_STORE["status"] = "error"
        LAST_TASK_RESULT_STORE["result"] = None
        LAST_TASK_RESULT_STORE["error_message"] = str(e)

        print(f"Error during task execution: {e}")
        # Log the full exception for debugging
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"An error occurred during task execution: {str(e)}")


@app.get("/tasks/{task_id}", response_model=TaskResponse)
async def get_task_status(task_id: str):
    """
    Retrieves the status and result of a task.
    For synchronous tasks, this will effectively return the result of the last executed task
    if the task_id matches the one used (e.g., "sync_task_001").
    """
    print(f"Received request for task_id: {task_id}")
    if LAST_TASK_RESULT_STORE["task_id"] == task_id:
        if LAST_TASK_RESULT_STORE["status"] == "error":
            # If the last task had an error, we might want to reflect that.
            # However, TaskResponse doesn't have an error field in this version.
            # Let's return the stored status and result (which would be None for result if error).
            return TaskResponse(
                task_id=task_id,
                status=LAST_TASK_RESULT_STORE["status"],
                result=LAST_TASK_RESULT_STORE["result"] if LAST_TASK_RESULT_STORE["result"] else LAST_TASK_RESULT_STORE["error_message"]
            )
        return TaskResponse(
            task_id=LAST_TASK_RESULT_STORE["task_id"],
            status=LAST_TASK_RESULT_STORE["status"],
            result=LAST_TASK_RESULT_STORE["result"]
        )
    elif task_id == "sync_task_001" and LAST_TASK_RESULT_STORE["task_id"] is None:
        # If specifically querying the known sync ID but nothing has run yet
        return TaskResponse(
            task_id=task_id,
            status="pending",
            result="No synchronous task has been executed yet."
        )
    else:
        print(f"Task with id '{task_id}' not found in simple store.")
        raise HTTPException(status_code=404, detail=f"Task with id '{task_id}' not found.")


# --- Main entry point for running the server with Uvicorn (for development) ---
# To run this: uvicorn api_server:app --reload --port 8000
# You would typically run this command in your Termux terminal from the 'your-project' directory.
if __name__ == "__main__":
    import uvicorn
    # Get port from environment variable or default to 8000
    api_port = int(os.getenv("API_PORT", "8000"))
    print(f"Starting Uvicorn server on http://0.0.0.0:{api_port}")
    uvicorn.run(app, host="0.0.0.0", port=api_port)

# Note: For Termux, 0.0.0.0 makes it accessible from other devices on the network
# (e.g., your PC browser pointing to your phone's IP address and the port).
# If you only want to access it on the phone itself, you can use "127.0.0.1".
```
