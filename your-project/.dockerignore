# Python stuff
venv/
env/
__pycache__/
*.py[cod]
*$py.class
*.egg-info/
.Python
pip-log.txt
pip-delete-this-directory.txt
# Build artifacts
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
# IDE / OS specific
.git
.vscode/
.idea/
*.sublime-project
*.sublime-workspace
*.DS_Store
# Docker temporary files
docker-compose.override.yml
# If you have local data or test files not needed in the image
# data/
# local_test_files/
# *.tmp
# *.bak
# Large media files not part of the runtime
# *.mp4
# *.zip
# *.tar.gz

# Don't ignore the .env file if your Docker CMD relies on it being present
# and you are not passing all env vars through docker compose `environment`
# !.env
