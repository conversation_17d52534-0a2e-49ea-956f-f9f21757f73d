"""
OpenAI SDK Integration
Provides easy access to OpenAI's API for various AI tasks
"""

import os
from typing import List, Dict, Any, Optional
from openai import OpenAI
from dotenv import load_dotenv

load_dotenv()

class OpenAIClient:
    """Unified OpenAI client for various AI tasks"""
    
    def __init__(self, api_key: Optional[str] = None):
        """Initialize OpenAI client with API key"""
        self.api_key = api_key or os.getenv('OPENAI_API_KEY')
        if not self.api_key:
            raise ValueError("OpenAI API key not found. Set OPENAI_API_KEY environment variable.")
        
        self.client = OpenAI(api_key=self.api_key)
    
    def chat_completion(self, 
                       messages: List[Dict[str, str]], 
                       model: str = "gpt-4o-mini",
                       temperature: float = 0.7,
                       max_tokens: int = 1000) -> str:
        """Generate chat completion"""
        try:
            response = self.client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens
            )
            return response.choices[0].message.content
        except Exception as e:
            raise Exception(f"OpenAI API error: {str(e)}")
    
    def generate_image(self, 
                      prompt: str, 
                      size: str = "1024x1024",
                      quality: str = "standard") -> str:
        """Generate image using DALL-E"""
        try:
            response = self.client.images.generate(
                model="dall-e-3",
                prompt=prompt,
                size=size,
                quality=quality,
                n=1
            )
            return response.data[0].url
        except Exception as e:
            raise Exception(f"DALL-E API error: {str(e)}")
    
    def create_embedding(self, text: str, model: str = "text-embedding-3-small") -> List[float]:
        """Create text embedding"""
        try:
            response = self.client.embeddings.create(
                model=model,
                input=text
            )
            return response.data[0].embedding
        except Exception as e:
            raise Exception(f"OpenAI Embedding API error: {str(e)}")
    
    def transcribe_audio(self, audio_file_path: str, model: str = "whisper-1") -> str:
        """Transcribe audio to text"""
        try:
            with open(audio_file_path, "rb") as audio_file:
                response = self.client.audio.transcriptions.create(
                    model=model,
                    file=audio_file
                )
            return response.text
        except Exception as e:
            raise Exception(f"Whisper API error: {str(e)}")
    
    def text_to_speech(self, text: str, voice: str = "alloy", model: str = "tts-1") -> bytes:
        """Convert text to speech"""
        try:
            response = self.client.audio.speech.create(
                model=model,
                voice=voice,
                input=text
            )
            return response.content
        except Exception as e:
            raise Exception(f"TTS API error: {str(e)}")
