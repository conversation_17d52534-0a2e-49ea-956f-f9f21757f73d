"""
Google Tools SDK Integration
Provides access to Google APIs for various services
"""

import os
import json
from typing import Dict, List, Any, Optional
from googleapiclient.discovery import build
from google.oauth2 import service_account
from google.cloud import storage, aiplatform
from dotenv import load_dotenv

load_dotenv()

class GoogleTools:
    """Unified Google tools client for various Google services"""
    
    def __init__(self):
        """Initialize Google tools with credentials"""
        self.credentials_path = os.getenv('GOOGLE_APPLICATION_CREDENTIALS')
        if not self.credentials_path:
            raise ValueError("Google credentials path not found. Set GOOGLE_APPLICATION_CREDENTIALS environment variable.")
        
        self.credentials = service_account.Credentials.from_service_account_file(
            self.credentials_path
        )
        
        # Initialize services
        self._init_services()
    
    def _init_services(self):
        """Initialize Google service clients"""
        # Google Drive
        self.drive_service = build('drive', 'v3', credentials=self.credentials)
        
        # Google Sheets
        self.sheets_service = build('sheets', 'v4', credentials=self.credentials)
        
        # Google Calendar
        self.calendar_service = build('calendar', 'v3', credentials=self.credentials)
        
        # Google Cloud Storage
        self.storage_client = storage.Client(credentials=self.credentials)
        
        # Google Cloud AI Platform
        aiplatform.init(credentials=self.credentials)
    
    # Google Drive Methods
    def list_drive_files(self, folder_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """List files in Google Drive"""
        try:
            query = f"'{folder_id}' in parents" if folder_id else None
            results = self.drive_service.files().list(
                q=query,
                pageSize=100,
                fields="nextPageToken, files(id, name, mimeType)"
            ).execute()
            return results.get('files', [])
        except Exception as e:
            raise Exception(f"Google Drive API error: {str(e)}")
    
    def upload_file_to_drive(self, file_path: str, file_name: str, folder_id: Optional[str] = None) -> str:
        """Upload file to Google Drive"""
        try:
            from googleapiclient.http import MediaFileUpload
            
            file_metadata = {'name': file_name}
            if folder_id:
                file_metadata['parents'] = [folder_id]
            
            media = MediaFileUpload(file_path)
            file = self.drive_service.files().create(
                body=file_metadata,
                media_body=media,
                fields='id'
            ).execute()
            return file.get('id')
        except Exception as e:
            raise Exception(f"Google Drive upload error: {str(e)}")
    
    # Google Sheets Methods
    def read_sheet_data(self, spreadsheet_id: str, range_name: str) -> List[List[str]]:
        """Read data from Google Sheets"""
        try:
            result = self.sheets_service.spreadsheets().values().get(
                spreadsheetId=spreadsheet_id,
                range=range_name
            ).execute()
            return result.get('values', [])
        except Exception as e:
            raise Exception(f"Google Sheets API error: {str(e)}")
    
    def write_sheet_data(self, spreadsheet_id: str, range_name: str, values: List[List[str]]) -> Dict[str, Any]:
        """Write data to Google Sheets"""
        try:
            body = {'values': values}
            result = self.sheets_service.spreadsheets().values().update(
                spreadsheetId=spreadsheet_id,
                range=range_name,
                valueInputOption='RAW',
                body=body
            ).execute()
            return result
        except Exception as e:
            raise Exception(f"Google Sheets API error: {str(e)}")
    
    # Google Calendar Methods
    def list_calendar_events(self, calendar_id: str = 'primary', max_results: int = 10) -> List[Dict[str, Any]]:
        """List events from Google Calendar"""
        try:
            events_result = self.calendar_service.events().list(
                calendarId=calendar_id,
                maxResults=max_results,
                singleEvents=True,
                orderBy='startTime'
            ).execute()
            return events_result.get('items', [])
        except Exception as e:
            raise Exception(f"Google Calendar API error: {str(e)}")
    
    def create_calendar_event(self, calendar_id: str, event_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new calendar event"""
        try:
            event = self.calendar_service.events().insert(
                calendarId=calendar_id,
                body=event_data
            ).execute()
            return event
        except Exception as e:
            raise Exception(f"Google Calendar API error: {str(e)}")
    
    # Google Cloud Storage Methods
    def list_buckets(self) -> List[str]:
        """List all Google Cloud Storage buckets"""
        try:
            buckets = self.storage_client.list_buckets()
            return [bucket.name for bucket in buckets]
        except Exception as e:
            raise Exception(f"Google Cloud Storage API error: {str(e)}")
    
    def upload_to_gcs(self, bucket_name: str, file_path: str, destination_blob_name: str) -> str:
        """Upload file to Google Cloud Storage"""
        try:
            bucket = self.storage_client.bucket(bucket_name)
            blob = bucket.blob(destination_blob_name)
            blob.upload_from_filename(file_path)
            return f"gs://{bucket_name}/{destination_blob_name}"
        except Exception as e:
            raise Exception(f"Google Cloud Storage upload error: {str(e)}")
    
    def download_from_gcs(self, bucket_name: str, source_blob_name: str, destination_file_name: str) -> str:
        """Download file from Google Cloud Storage"""
        try:
            bucket = self.storage_client.bucket(bucket_name)
            blob = bucket.blob(source_blob_name)
            blob.download_to_filename(destination_file_name)
            return destination_file_name
        except Exception as e:
            raise Exception(f"Google Cloud Storage download error: {str(e)}")
    
    # Google AI Platform Methods
    def predict_with_vertex_ai(self, project_id: str, location: str, endpoint_id: str, instances: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Make predictions using Vertex AI"""
        try:
            endpoint = aiplatform.Endpoint(
                endpoint_name=f"projects/{project_id}/locations/{location}/endpoints/{endpoint_id}"
            )
            predictions = endpoint.predict(instances=instances)
            return {
                "predictions": predictions.predictions,
                "deployed_model_id": predictions.deployed_model_id
            }
        except Exception as e:
            raise Exception(f"Vertex AI API error: {str(e)}")
