"""
Robust SDK Integration
Fault-tolerant OpenAI, Google, and Social Media SDK integrations
"""

import os
import logging
from typing import Dict, List, Any, Optional
from dotenv import load_dotenv

load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RobustOpenAIClient:
    """Fault-tolerant OpenAI client"""
    
    def __init__(self):
        self.api_key = os.getenv('OPENAI_API_KEY')
        self.client = None
        self.available = False
        
        if self.api_key:
            try:
                from openai import OpenAI
                self.client = OpenAI(api_key=self.api_key)
                self.available = True
                logger.info("OpenAI client initialized successfully")
            except ImportError:
                logger.warning("OpenAI package not installed. Run: pip install openai")
            except Exception as e:
                logger.error(f"Failed to initialize OpenAI client: {str(e)}")
    
    def is_available(self) -> bool:
        return self.available
    
    def chat_completion(self, messages: List[Dict[str, str]], **kwargs) -> Optional[str]:
        if not self.available:
            return "OpenAI client not available. Please check your API key."
        try:
            response = self.client.chat.completions.create(messages=messages, **kwargs)
            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"OpenAI API error: {str(e)}")
            return None

class RobustGoogleTools:
    """Fault-tolerant Google tools client"""
    
    def __init__(self):
        self.credentials_path = os.getenv('GOOGLE_APPLICATION_CREDENTIALS')
        self.credentials = None
        self.available = False
        
        if self.credentials_path and os.path.exists(self.credentials_path):
            try:
                from googleapiclient.discovery import build
                from google.oauth2 import service_account
                
                self.credentials = service_account.Credentials.from_service_account_file(
                    self.credentials_path
                )
                self.available = True
                logger.info("Google tools client initialized successfully")
            except ImportError:
                logger.warning("Google API packages not installed. Run: pip install google-api-python-client google-auth")
            except Exception as e:
                logger.error(f"Failed to initialize Google tools client: {str(e)}")
    
    def is_available(self) -> bool:
        return self.available
    
    def list_drive_files(self) -> List[Dict[str, Any]]:
        if not self.available:
            return [{"error": "Google tools not available"}]
        try:
            from googleapiclient.discovery import build
            service = build('drive', 'v3', credentials=self.credentials)
            results = service.files().list(pageSize=10, fields="files(id, name)").execute()
            return results.get('files', [])
        except Exception as e:
            logger.error(f"Google Drive API error: {str(e)}")
            return [{"error": str(e)}]

class RobustSocialMediaManager:
    """Fault-tolerant social media client"""
    
    def __init__(self):
        self.instagram_username = os.getenv('INSTAGRAM_USERNAME')
        self.instagram_password = os.getenv('INSTAGRAM_PASSWORD')
        self.youtube_api_key = os.getenv('YOUTUBE_API_KEY')
        self.available = {
            'instagram': False,
            'youtube': False,
            'tiktok': False
        }
        
        # Instagram
        if self.instagram_username and self.instagram_password:
            try:
                from instagrapi import Client
                self.instagram_client = Client()
                self.available['instagram'] = True
                logger.info("Instagram client initialized successfully")
            except ImportError:
                logger.warning("Instagram package not installed. Run: pip install instagrapi")
            except Exception as e:
                logger.error(f"Instagram client error: {str(e)}")
        
        # YouTube
        if self.youtube_api_key:
            try:
                from googleapiclient.discovery import build
                self.youtube_client = build('youtube', 'v3', developerKey=self.youtube_api_key)
                self.available['youtube'] = True
                logger.info("YouTube client initialized successfully")
            except ImportError:
                logger.warning("Google API packages not installed")
            except Exception as e:
                logger.error(f"YouTube client error: {str(e)}")
    
    def get_available_services(self) -> Dict[str, bool]:
        return self.available
    
    def get_instagram_profile_info(self, username: str) -> Dict[str, Any]:
        if not self.available['instagram']:
            return {"error": "Instagram client not available"}
        try:
            user_info = self.instagram_client.user_info_by_username(username)
            return {
                'username': user_info.username,
                'followers': user_info.follower_count,
                'posts': user_info.media_count
            }
        except Exception as e:
            return {"error": str(e)}

# Create a unified client that handles all SDKs
class RobustSDKClient:
    """Unified client for all SDKs with fault tolerance"""
    
    def __init__(self):
        self.openai = RobustOpenAIClient()
        self.google = RobustGoogleTools()
        self.social = RobustSocialMediaManager()
    
    def get_status(self) -> Dict[str, bool]:
        """Get status of all SDKs"""
        return {
            'openai': self.openai.is_available(),
            'google': self.google.is_available(),
            'instagram': self.social.available['instagram'],
            'youtube': self.social.available['youtube']
        }
    
    def get_available_services(self) -> Dict[str, bool]:
        """Get available services"""
        status = self.get_status()
        return {
            'OpenAI': status['openai'],
            'Google Tools': status['google'],
            'Instagram': status['instagram'],
            'YouTube': status['youtube']
        }
