"""
Social Media SDK Integration
Provides unified access to TikTok, YouTube, and Instagram SDKs
"""

import os
import json
from typing import Dict, List, Any, Optional
from dotenv import load_dotenv

load_dotenv()

class SocialMediaManager:
    """Unified social media client for TikTok, YouTube, and Instagram"""
    
    def __init__(self):
        """Initialize social media clients with credentials"""
        self.instagram_username = os.getenv('INSTAGRAM_USERNAME')
        self.instagram_password = os.getenv('INSTAGRAM_PASSWORD')
        self.tiktok_session_id = os.getenv('TIKTOK_SESSION_ID')
        self.youtube_api_key = os.getenv('YOUTUBE_API_KEY')
    
    # Instagram Methods
    def instagram_login(self) -> bool:
        """Login to Instagram"""
        try:
            from instagrapi import Client
            self.instagram_client = Client()
            self.instagram_client.login(self.instagram_username, self.instagram_password)
            return True
        except Exception as e:
            print(f"Instagram login error: {str(e)}")
            return False
    
    def get_instagram_profile_info(self, username: str) -> Dict[str, Any]:
        """Get Instagram profile information"""
        try:
            user_info = self.instagram_client.user_info_by_username(username)
            return {
                'username': user_info.username,
                'full_name': user_info.full_name,
                'followers': user_info.follower_count,
                'following': user_info.following_count,
                'posts': user_info.media_count,
                'bio': user_info.biography,
                'profile_pic_url': user_info.profile_pic_url
            }
        except Exception as e:
            raise Exception(f"Instagram API error: {str(e)}")
    
    def get_instagram_posts(self, username: str, amount: int = 10) -> List[Dict[str, Any]]:
        """Get Instagram posts for a user"""
        try:
            user_id = self.instagram_client.user_id_from_username(username)
            medias = self.instagram_client.user_medias(user_id, amount)
            posts = []
            for media in medias:
                posts.append({
                    'id': media.id,
                    'caption': media.caption_text,
                    'likes': media.like_count,
                    'comments': media.comment_count,
                    'url': media.thumbnail_url,
                    'timestamp': media.taken_at
                })
            return posts
        except Exception as e:
            raise Exception(f"Instagram API error: {str(e)}")
    
    # TikTok Methods
    def get_tiktok_user_info(self, username: str) -> Dict[str, Any]:
        """Get TikTok user information"""
        try:
            # This is a placeholder - implement with actual TikTok API
            return {
                'username': username,
                'followers': 0,
                'following': 0,
                'likes': 0,
                'videos': 0,
                'bio': '',
                'profile_pic_url': ''
            }
        except Exception as e:
            raise Exception(f"TikTok API error: {str(e)}")
    
    def get_tiktok_videos(self, username: str, amount: int = 10) -> List[Dict[str, Any]]:
        """Get TikTok videos for a user"""
        try:
            # This is a placeholder - implement with actual TikTok API
            return []
        except Exception as e:
            raise Exception(f"TikTok API error: {str(e)}")
    
    # YouTube Methods
    def get_youtube_channel_info(self, channel_id: str) -> Dict[str, Any]:
        """Get YouTube channel information"""
        try:
            from googleapiclient.discovery import build
            
            youtube = build('youtube', 'v3', developerKey=self.youtube_api_key)
            request = youtube.channels().list(
                part='snippet,statistics',
                id=channel_id
            )
            response = request.execute()
            
            if response['items']:
                channel = response['items'][0]
                return {
                    'channel_id': channel['id'],
                    'title': channel['snippet']['title'],
                    'description': channel['snippet']['description'],
                    'subscribers': int(channel['statistics']['subscriberCount']),
                    'videos': int(channel['statistics']['videoCount']),
                    'views': int(channel['statistics']['viewCount']),
                    'profile_pic_url': channel['snippet']['thumbnails']['high']['url']
                }
            return {}
        except Exception as e:
            raise Exception(f"YouTube API error: {str(e)}")
    
    def get_youtube_videos(self, channel_id: str, max_results: int = 10) -> List[Dict[str, Any]]:
        """Get YouTube videos for a channel"""
        try:
            from googleapiclient.discovery import build
            
            youtube = build('youtube', 'v3', developerKey=self.youtube_api_key)
            request = youtube.search().list(
                part='snippet',
                channelId=channel_id,
                maxResults=max_results,
                order='date',
                type='video'
            )
            response = request.execute()
            
            videos = []
            for item in response['items']:
                videos.append({
                    'video_id': item['id']['videoId'],
                    'title': item['snippet']['title'],
                    'description': item['snippet']['description'],
                    'thumbnail_url': item['snippet']['thumbnails']['high']['url'],
                    'published_at': item['snippet']['publishedAt'],
                    'channel_title': item['snippet']['channelTitle']
                })
            return videos
        except Exception as e:
            raise Exception(f"YouTube API error: {str(e)}")
    
    def get_youtube_video_details(self, video_id: str) -> Dict[str, Any]:
        """Get detailed information about a YouTube video"""
        try:
            from googleapiclient.discovery import build
            
            youtube = build('youtube', 'v3', developerKey=self.youtube_api_key)
            request = youtube.videos().list(
                part='snippet,statistics',
                id=video_id
            )
            response = request.execute()
            
            if response['items']:
                video = response['items'][0]
                return {
                    'video_id': video['id'],
                    'title': video['snippet']['title'],
                    'description': video['snippet']['description'],
                    'thumbnail_url': video['snippet']['thumbnails']['high']['url'],
                    'published_at': video['snippet']['publishedAt'],
                    'channel_title': video['snippet']['channelTitle'],
                    'view_count': int(video['statistics']['viewCount']),
                    'like_count': int(video['statistics']['likeCount']),
                    'comment_count': int(video['statistics']['commentCount'])
                }
            return {}
        except Exception as e:
            raise Exception(f"YouTube API error: {str(e)}")
    
    # Social Media Analytics
    def get_social_media_analytics(self, platform: str, username: str) -> Dict[str, Any]:
        """Get comprehensive social media analytics"""
        analytics = {}
        
        if platform.lower() == 'instagram':
            analytics['instagram'] = self.get_instagram_profile_info(username)
        elif platform.lower() == 'tiktok':
            analytics['tiktok'] = self.get_tiktok_user_info(username)
        elif platform.lower() == 'youtube':
            analytics['youtube'] = self.get_youtube_channel_info(username)
        
        return analytics
    
    def get_cross_platform_analytics(self, username: str) -> Dict[str, Any]:
        """Get analytics across all platforms"""
        return {
            'instagram': self.get_instagram_profile_info(username),
            'tiktok': self.get_tiktok_user_info(username),
            'youtube': self.get_youtube_channel_info(username)
        }
