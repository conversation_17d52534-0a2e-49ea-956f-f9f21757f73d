"""
PodGPT Tools
Custom tools for PodGPT that can be used by AI agents
"""

from crewai.tools import tool
from typing import Dict, Any
from .podgpt_client import podgpt_client

@tool("HumanLikeExplanationTool")
def explain_like_human(topic: str, context: str = "") -> str:
    """
    Explain a topic in a human-like way, similar to how it was trained on science podcasts.
    
    Args:
        topic (str): The topic to explain
        context (str): Additional context for the explanation
        
    Returns:
        str: Human-like explanation of the topic
    """
    try:
        explanation = podgpt_client.explain_like_human(topic, context)
        return explanation
    except Exception as e:
        return f"Error generating human-like explanation: {str(e)}"

@tool("ClientRebuttalResponseTool")
def respond_to_client_rebuttal(client_statement: str, product_info: str) -> str:
    """
    Generate a human-like response to a client's rebuttal or concern.
    
    Args:
        client_statement (str): The client's statement or question
        product_info (str): Information about the insurance product
        
    Returns:
        str: Human-like response addressing the client's concerns
    """
    try:
        response = podgpt_client.generate_response_to_rebuttal(client_statement, product_info)
        return response
    except Exception as e:
        return f"Error generating response to client rebuttal: {str(e)}"

@tool("ProductPitchingTool")
def pitch_product_to_client(client_profile: str, product_info: str) -> str:
    """
    Pitch an insurance product to a client in a human-like, conversational way.
    
    Args:
        client_profile (str): Information about the client's needs and profile
        product_info (str): Information about the insurance product
        
    Returns:
        str: Human-like product pitch tailored to the client
    """
    try:
        prompt = f"""
        You are an insurance expert pitching a product to a client.
        Client profile: {client_profile}
        Product information: {product_info}
        
        Create a pitch that explains how this product specifically addresses the client's needs,
        in a human-like, conversational way similar to how you might explain something complex 
        on a science podcast. Be empathetic, clear, and focus on benefits rather than features.
        """
        
        # For now, we'll use the explain_like_human method as a placeholder
        # In a real implementation, this would be a more specific method
        pitch = podgpt_client.explain_like_human(
            f"how {product_info} benefits a client with profile {client_profile}",
            "Pitch this insurance product in a human-like, conversational way"
        )
        return pitch
    except Exception as e:
        return f"Error generating product pitch: {str(e)}"
