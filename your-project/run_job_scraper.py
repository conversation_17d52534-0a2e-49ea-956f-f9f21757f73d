import sys
import asyncio
from dotenv import load_dotenv

load_dotenv()

from enhanced_agent_system import ComprehensiveAgentSystem

async def main():
    system = ComprehensiveAgentSystem()
    await system.initialize_system()
    results = await system.run_comprehensive_automation()
    print("Job scraping and email automation completed.")
    print(results)

if __name__ == "__main__":
    asyncio.run(main())
