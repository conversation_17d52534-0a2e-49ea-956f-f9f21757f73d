#!/usr/bin/env python3
"""
Test script to verify all SDK integrations are working correctly
"""

import os
import sys
from dotenv import load_dotenv

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sdk_integrations import OpenAIClient, GoogleTools, SocialMediaManager

def test_openai_integration():
    """Test OpenAI SDK integration"""
    print("Testing OpenAI SDK...")
    try:
        # Test basic initialization
        client = OpenAIClient()
        print("✓ OpenAI client initialized successfully")
        
        # Test simple chat completion
        response = client.chat_completion([
            {"role": "user", "content": "Hello, this is a test."}
        ])
        print("✓ OpenAI chat completion working")
        return True
    except Exception as e:
        print(f"✗ OpenAI test failed: {str(e)}")
        return False

def test_google_tools_integration():
    """Test Google Tools SDK integration"""
    print("\nTesting Google Tools SDK...")
    try:
        # Test basic initialization
        google_tools = GoogleTools()
        print("✓ Google Tools client initialized successfully")
        
        # Test Google Drive connection
        buckets = google_tools.list_buckets()
        print("✓ Google Cloud Storage connection working")
        return True
    except Exception as e:
        print(f"✗ Google Tools test failed: {str(e)}")
        return False

def test_social_media_integration():
    """Test Social Media SDK integration"""
    print("\nTesting Social Media SDK...")
    try:
        # Test basic initialization
        social_manager = SocialMediaManager()
        print("✓ Social Media manager initialized successfully")
        
        # Test YouTube API connection
        if social_manager.youtube_api_key:
            print("✓ YouTube API key configured")
        else:
            print("⚠ YouTube API key not configured")
        return True
    except Exception as e:
        print(f"✗ Social Media test failed: {str(e)}")
        return False

def main():
    """Run all integration tests"""
    print("=" * 60)
    print("SDK Integration Test Suite")
    print("=" * 60)
    
    # Load environment variables
    load_dotenv()
    
    # Test results
    results = {
        'OpenAI': test_openai_integration(),
        'Google Tools': test_google_tools_integration(),
        'Social Media': test_social_media_integration()
    }
    
    print("\n" + "=" * 60)
    print("Test Results Summary")
    print("=" * 60)
    
    for service, passed in results.items():
        status = "✓ PASSED" if passed else "✗ FAILED"
        print(f"{service}: {status}")
    
    # Overall status
    all_passed = all(results.values())
    print(f"\nOverall Status: {'✓ ALL TESTS PASSED' if all_passed else '✗ SOME TESTS FAILED'}")
    
    if not all_passed:
        print("\nPlease check the SDK_SETUP_GUIDE.md for troubleshooting steps")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
