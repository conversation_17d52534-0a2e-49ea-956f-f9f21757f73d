# Core Agent Framework
crewai
crewai-tools

# Foundational Library for Agents (used for ChatOpenAI wrapper to connect to LM Studio)
langchain-openai==0.1.8

# Additional Langchain components (good to have, might be used by specific tools or future LLM setups)
langchain-community

# For Computer Vision Capabilities (tool dependency)
opencv-python-headless

# Utilities
python-dotenv==1.0.1

# API Framework
fastapi
uvicorn[standard] # For serving the FastAPI app

# For Browser Automation (Social Media Scraping)
playwright

# OpenAI SDK
openai>=1.0.0

# Google Tools SDKs
google-api-python-client
google-auth-httplib2
google-auth-oauthlib
google-cloud-storage
google-cloud-aiplatform

# Social Media SDKs
youtube-search-python
pytube
instaloader

# Additional utilities for social media
requests
beautifulsoup4
selenium
webdriver-manager

# Image processing for social media
Pillow
