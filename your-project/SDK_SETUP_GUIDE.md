# SDK Integration Setup Guide

This guide will help you install and integrate SDKs for OpenAI, Google tools, and social media platforms (TikTok, YouTube, Instagram).

## Quick Setup

### 1. Install Dependencies

```bash
# Update your requirements.txt with the new dependencies
cp your-project/requirements_updated.txt your-project/requirements.txt

# Install all dependencies
pip install -r your-project/requirements.txt
```

### 2. Environment Variables Setup

Create a `.env` file in your project root with the following variables:

```bash
# OpenAI
OPENAI_API_KEY=your_openai_api_key_here

# Google Cloud
GOOGLE_APPLICATION_CREDENTIALS=path/to/your/google-credentials.json

# Instagram
INSTAGRAM_USERNAME=your_instagram_username
INSTAGRAM_PASSWORD=your_instagram_password

# TikTok
TIKTOK_SESSION_ID=your_tiktok_session_id

# YouTube
YOUTUBE_API_KEY=your_youtube_api_key
```

### 3. Google Cloud Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable required APIs:
   - Google Drive API
   - Google Sheets API
   - Google Calendar API
   - YouTube Data API v3
   - Cloud Storage API
   - Vertex AI API
4. Create service account credentials
5. Download the JSON credentials file
6. Set the path in `GOOGLE_APPLICATION_CREDENTIALS`

### 4. Usage Examples

#### OpenAI Integration
```python
from sdk_integrations import OpenAIClient

# Initialize client
openai_client = OpenAIClient()

# Chat completion
response = openai_client.chat_completion([
    {"role": "user", "content": "What's the weather like today?"}
])
print(response)

# Generate image
image_url = openai_client.generate_image("A beautiful sunset over mountains")
print(image_url)
```

#### Google Tools Integration
```python
from sdk_integrations import GoogleTools

# Initialize client
google_tools = GoogleTools()

# List Google Drive files
files = google_tools.list_drive_files()
print(files)

# Read Google Sheets data
data = google_tools.read_sheet_data('your-spreadsheet-id', 'Sheet1!A1:D10')
print(data)

# Upload to Google Cloud Storage
url = google_tools.upload_to_gcs('your-bucket-name', 'local-file.txt', 'remote-file.txt')
print(url)
```

#### Social Media Integration
```python
from sdk_integrations import SocialMediaManager

# Initialize manager
social_manager = SocialMediaManager()

# Instagram
if social_manager.instagram_login():
    profile = social_manager.get_instagram_profile_info('username')
    print(profile)

# YouTube
channel_info = social_manager.get_youtube_channel_info('channel-id')
print(channel_info)

# Cross-platform analytics
analytics = social_manager.get_cross_platform_analytics('username')
print(analytics)
```

## Advanced Configuration

### Instagram Setup
For Instagram automation, you may need to:
1. Use Instagram Basic Display API for public data
2. Use Instagram Graph API for business accounts
3. Handle rate limits and authentication

### TikTok Setup
For TikTok integration:
1. Use TikTok for Developers API
2. Handle session management
3. Respect rate limits

### YouTube Setup
For YouTube integration:
1. Enable YouTube Data API v3
2. Handle quota limits
3. Use OAuth 2.0 for user data

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure all dependencies are installed
2. **Authentication Errors**: Check API keys and credentials
3. **Rate Limits**: Implement retry logic and respect rate limits
4. **Permissions**: Ensure proper API permissions are granted

### Debug Mode

Enable debug logging:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Security Best Practices

1. Never commit API keys to version control
2. Use environment variables for sensitive data
3. Implement proper error handling
4. Respect rate limits and terms of service
5. Use secure storage for credentials

## Testing

Run the provided test script to verify all integrations:
```bash
python your-project/test_sdk_integrations.py
```

## Support

For issues with specific SDKs:
- OpenAI: https://platform.openai.com/docs
- Google Cloud: https://cloud.google.com/docs
- Instagram: https://developers.facebook.com/docs/instagram
- TikTok: https://developers.tiktok.com/
- YouTube: https://developers.google.com/youtube/v3
```

## Next Steps

1. Set up your environment variables
2. Test each integration individually
3. Implement error handling and retry logic
4. Add monitoring and logging
5. Deploy with proper security measures
