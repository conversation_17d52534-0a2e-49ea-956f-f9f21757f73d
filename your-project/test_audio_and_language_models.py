#!/usr/bin/env python3
"""
Test script to verify audio processing and language model integrations
"""

import os
import sys
from dotenv import load_dotenv

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_audio_processing():
    """Test audio processing components"""
    print("Testing Audio Processing Components...")
    try:
        import sys
        sys.path.append('.')
        from audio_processing import AudioProcessingClient, transcribe_audio, understand_audio_context, validate_audio_file
        
        # Test client initialization
        client = AudioProcessingClient()
        print("✓ AudioProcessingClient initialized successfully")
        
        # Test available models
        models = client.get_available_models()
        print(f"✓ Available models: {models}")
        
        # Test tools
        print("✓ Audio tools imported successfully")
        return True
    except Exception as e:
        print(f"✗ Audio processing test failed: {str(e)}")
        return False

def test_language_models():
    """Test language model components"""
    print("\nTesting Language Model Components...")
    try:
        import sys
        sys.path.append('.')
        from language_models import PodGPTClient, podgpt_client, explain_like_human, respond_to_client_rebuttal, pitch_product_to_client
        
        # Test client initialization
        client = PodGPTClient()
        print("✓ PodGPTClient initialized successfully")
        
        # Test global instance
        if podgpt_client.is_available():
            print("✓ PodGPT is available")
        else:
            print("⚠ PodGPT is not available (expected in placeholder implementation)")
        
        # Test tools
        print("✓ Language model tools imported successfully")
        return True
    except Exception as e:
        print(f"✗ Language model test failed: {str(e)}")
        return False

def test_music_agents():
    """Test music agent components"""
    print("\nTesting Music Agent Components...")
    try:
        import sys
        sys.path.append('.')
        from agents import MusicLabelAgents, MusicLabelCrew
        
        # Test agent creation
        analyst = MusicLabelAgents.create_music_analyst()
        researcher = MusicLabelAgents.create_industry_researcher()
        coordinator = MusicLabelAgents.create_licensing_coordinator()
        print("✓ Music agents created successfully")
        
        # Test crew creation
        crew = MusicLabelCrew.create_sync_licensing_crew()
        print("✓ Music licensing crew created successfully")
        
        return True
    except Exception as e:
        print(f"✗ Music agent test failed: {str(e)}")
        return False

def test_flo_insurance_agents():
    """Test FLO insurance agent enhancements"""
    print("\nTesting FLO Insurance Agent Enhancements...")
    try:
        import sys
        sys.path.append('.')
        from agents import FLOInsuranceAgents
        
        # Test new agent creation
        client_relations_manager = FLOInsuranceAgents.create_client_relations_manager()
        print("✓ Client Relations Manager agent created successfully")
        
        return True
    except Exception as e:
        print(f"✗ FLO insurance agent test failed: {str(e)}")
        return False

def main():
    """Run all integration tests"""
    print("=" * 60)
    print("Audio Processing and Language Model Integration Test Suite")
    print("=" * 60)
    
    # Load environment variables
    load_dotenv()
    
    # Test results
    results = {
        'Audio Processing': test_audio_processing(),
        'Language Models': test_language_models(),
        'Music Agents': test_music_agents(),
        'FLO Insurance Agents': test_flo_insurance_agents()
    }
    
    print("\n" + "=" * 60)
    print("Test Results Summary")
    print("=" * 60)
    
    for component, passed in results.items():
        status = "✓ PASSED" if passed else "✗ FAILED"
        print(f"{component}: {status}")
    
    # Overall status
    all_passed = all(results.values())
    print(f"\nOverall Status: {'✓ ALL TESTS PASSED' if all_passed else '✗ SOME TESTS FAILED'}")
    
    if not all_passed:
        print("\nSome components may be placeholders and need further implementation.")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
