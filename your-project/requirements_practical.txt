# Core Agent Framework
crewai>=0.148.0
crewai-tools>=0.58.0

# Foundational Library for Agents (used for ChatOpenAI wrapper to connect to LM Studio)
langchain-openai==0.1.8

# Additional Langchain components (good to have, might be used by specific tools or future LLM setups)
langchain-community

# For Computer Vision Capabilities (tool dependency)
opencv-python-headless

# Utilities
python-dotenv==1.0.1

# API Framework
fastapi
uvicorn[standard] # For serving the FastAPI app

# For Browser Automation (Social Media Scraping)
playwright

# OpenAI SDK
openai>=1.0.0

# Google Tools SDKs
google-api-python-client
google-auth-httplib2
google-auth-oauthlib
google-cloud-storage
google-cloud-aiplatform

# Social Media SDKs
youtube-search-python
pytube
instaloader

# Additional utilities for social media
requests
beautifulsoup4
selenium
webdriver-manager

# Image processing for social media
Pillow

# Audio processing dependencies
soundfile
librosa
pydub

# For speech recognition
speechrecognition

# For text-to-speech
gTTS

# For web searching
duckduckgo-search

# For PDF processing
pymupdf

# For document processing
unstructured

# For email processing
imaplib2

# For database operations
sqlalchemy

# For data manipulation
pandas
numpy

# For web scraping
scrapy

# For API rate limiting
ratelimit

# For caching
cachetools

# For async operations
asyncio-mqtt

# For YAML processing
pyyaml

# For JSON processing
orjson

# For encryption
cryptography

# For system monitoring
psutil

# For date/time operations
python-dateutil

# For URL handling
urllib3

# For HTTP operations
httpx

# For natural language processing
nltk
spacy

# For plotting (optional)
matplotlib
seaborn

# For machine learning (optional)
scikit-learn

# For deep learning (optional)
torch
torchaudio

# For music information retrieval
mido
pretty-midi

# For audio feature extraction
essentia

# For audio source separation
spleeter

# For audio enhancement
noisereduce

# For audio generation (Facebook's AudioCraft)
audiocraft

# For audio augmentation
torch-audio-transforms
