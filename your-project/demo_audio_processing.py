#!/usr/bin/env python3
"""
Demo script to showcase audio processing capabilities
"""

import os
import sys
import tempfile
from dotenv import load_dotenv

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from audio_processing import AudioProcessingClient
from audio_processing.audio_tools import transcribe_audio, understand_audio_context, validate_audio_file
from agents import MusicLabelAgents, MusicLabelCrew

def demo_audio_transcription():
    """Demo audio transcription capabilities"""
    print("=== Audio Transcription Demo ===")
    
    # Create a mock audio file for demonstration
    with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as tmp_file:
        tmp_filename = tmp_file.name
        # Write some dummy data to make it a valid file
        tmp_file.write(b"RIFF$\x00\x00\x00WAVEfmt \x10\x00\x00\x00\x01\x00\x01\x00D\xac\x00\x00\x88X\x01\x00\x02\x00\x10\x00data\x00\x00\x00\x00")
    
    try:
        # Validate the audio file
        validation_result = validate_audio_file._run(tmp_filename)
        print(f"Audio File Validation:\n{validation_result}")
        
        # Try to transcribe with different models
        print("\n--- Transcription with Whisper (fallback) ---")
        transcription = transcribe_audio._run(tmp_filename, "whisper")
        print(f"Transcription result: {transcription}")
        
        print("\n--- Transcription with Audio Flamingo ---")
        transcription = transcribe_audio._run(tmp_filename, "audio_flamingo")
        print(f"Transcription result: {transcription}")
        
        print("\n--- Transcription with Mistral Audio ---")
        transcription = transcribe_audio._run(tmp_filename, "mistral_audio")
        print(f"Transcription result: {transcription}")
        
    finally:
        # Clean up the temporary file
        os.unlink(tmp_filename)

def demo_audio_context_understanding():
    """Demo audio context understanding capabilities"""
    print("\n=== Audio Context Understanding Demo ===")
    
    # Create a mock audio file for demonstration
    with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as tmp_file:
        tmp_filename = tmp_file.name
        # Write some dummy data to make it a valid file
        tmp_file.write(b"RIFF$\x00\x00\x00WAVEfmt \x10\x00\x00\x00\x01\x00\x01\x00D\xac\x00\x00\x88X\x01\x00\x02\x00\x10\x00data\x00\x00\x00\x00")
    
    try:
        # Understand audio context with Audio Flamingo
        print("\n--- Context Understanding with Audio Flamingo ---")
        context = understand_audio_context._run(tmp_filename, "audio_flamingo")
        print(f"Context understanding result:\n{context}")
        
        # Understand audio context with Mistral Audio
        print("\n--- Context Understanding with Mistral Audio ---")
        context = understand_audio_context._run(tmp_filename, "mistral_audio")
        print(f"Context understanding result:\n{context}")
        
    finally:
        # Clean up the temporary file
        os.unlink(tmp_filename)

def demo_music_analysis():
    """Demo music analysis capabilities"""
    print("\n=== Music Analysis Demo ===")
    
    # Create music agents
    print("\n--- Creating Music Agents ---")
    music_analyst = MusicLabelAgents.create_music_analyst()
    industry_researcher = MusicLabelAgents.create_industry_researcher()
    licensing_coordinator = MusicLabelAgents.create_licensing_coordinator()
    
    print("✓ Music Analyst Agent created")
    print("✓ Industry Researcher Agent created")
    print("✓ Licensing Coordinator Agent created")
    
    # Create a sync licensing crew
    print("\n--- Creating Sync Licensing Crew ---")
    licensing_crew = MusicLabelCrew.create_sync_licensing_crew()
    print("✓ Sync Licensing Crew created")
    
    print(f"✓ Crew has {len(licensing_crew.agents)} agents")
    print(f"✓ Crew has {len(licensing_crew.tasks)} tasks")

def main():
    """Run all demos"""
    print("Audio Processing and Music Analysis Demo")
    print("=" * 50)
    
    # Load environment variables
    load_dotenv()
    
    # Run demos
    demo_audio_transcription()
    demo_audio_context_understanding()
    demo_music_analysis()
    
    print("\n" + "=" * 50)
    print("Demo completed successfully!")

if __name__ == "__main__":
    main()
