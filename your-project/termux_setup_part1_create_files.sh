#!/bin/bash

# <PERSON>ript to create project structure and populate files for the CrewAI LM Studio project
# Run this script in the directory where you want 'your-project' to be created.

PROJECT_DIR="your-project"

# Create base directories
mkdir -p "$PROJECT_DIR"
mkdir -p "$PROJECT_DIR/src"
mkdir -p "$PROJECT_DIR/agents"

# Create __init__.py files
touch "$PROJECT_DIR/src/__init__.py"
touch "$PROJECT_DIR/agents/__init__.py"

# Create agents/custom_tools.py
cat << 'EOF' > "$PROJECT_DIR/agents/custom_tools.py"
from crewai_tools import tool

@tool("CodeFileReaderTool")
def read_file(file_path: str) -> str:
    """Reads the content of a specified file from the codebase.
    Assumes 'src' directory is at the same level as 'main_agent_runner.py'.
    """
    try:
        # Correct path assuming this tool is called from main_agent_runner.py at project root
        with open(f"src/{file_path}", "r") as f:
            content = f.read()
        return content
    except FileNotFoundError:
        return f"Error: File not found at src/{file_path}"
    except Exception as e:
        return f"An unexpected error occurred while reading {file_path}: {e}"

@tool("ComputerVisionTool")
def analyze_image(image_path: str) -> str:
    """
    Analyzes an image and returns a description of its basic properties.
    """
    import cv2
    import os

    if not os.path.exists(image_path):
        return f"Error: Image file not found at path: {image_path}"

    try:
        image = cv2.imread(image_path)
        if image is None:
            return f"Error: Could not read image from path: {image_path}. Ensure it is a valid image file and OpenCV can access it."

        height, width, channels = image.shape
        description = f"Image properties: Width={width}px, Height={height}px, Channels={channels}."

        return description
    except Exception as e:
        return f"Error analyzing image at {image_path}: {e}"

@tool("JohnTheRipperTool")
def run_john_the_ripper(hash_file_path: str) -> str:
    """
    Runs John the Ripper on a specified hash file for authorized security audits.
    Requires explicit user approval. This tool is for security testing ONLY.
    """
    print("SECURITY WARNING: Attempting to run John the Ripper. This requires manual approval and setup.")

    import subprocess
    import os

    if not os.path.exists(hash_file_path):
        return f"Error: Hash file not found at path: {hash_file_path}"

    wordlist_path = "password.lst" # User may need to change this

    try:
        session_name = f"jtr_session_{os.path.basename(hash_file_path)}"
        print(f"Attempting to run John the Ripper on '{hash_file_path}' with wordlist '{wordlist_path}'. Session: {session_name}")

        command = [
            "john",
            f"--session={session_name}",
            f"--wordlist={wordlist_path}",
            "--rules",
            hash_file_path
        ]

        process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        stdout, stderr = process.communicate()

        show_command = [
            "john",
            f"--session={session_name}",
            "--show",
            hash_file_path
        ]
        show_process = subprocess.Popen(show_command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        show_stdout, show_stderr = show_process.communicate()

        results = f"John the Ripper Cracking Process Output for {hash_file_path}:\nSTDOUT:\n{stdout}\nSTDERR:\n{stderr}\n\n"
        results += f"John the Ripper Show Cracked Passwords Output:\nSTDOUT:\n{show_stdout}\nSTDERR:\n{show_stderr}\n"

        if show_process.returncode != 0 and not show_stdout:
             results += "Warning: '--show' command might have failed or no passwords cracked yet/found."
        elif not show_stdout.strip() and process.returncode == 0 :
            results += "No passwords cracked or shown for this session yet."

        return results

    except FileNotFoundError:
        return "Error: 'john' command not found. Ensure John the Ripper is installed and in the system's PATH."
    except Exception as e:
        return f"An unexpected error occurred with JohnTheRipperTool: {e}"
EOF

# Create agents/code_analysis_agents.py
cat << 'EOF' > "$PROJECT_DIR/agents/code_analysis_agents.py"
import os
from crewai import Agent
from langchain_openai import ChatOpenAI
from .custom_tools import read_file
from dotenv import load_dotenv

load_dotenv()

lm_studio_base_url = os.getenv("LM_STUDIO_URL")
lm_studio_api_key = os.getenv("LM_STUDIO_API_KEY", "not-needed")

llm = ChatOpenAI(
    openai_api_base=lm_studio_base_url,
    openai_api_key=lm_studio_api_key,
    model_name="local-model"
)

code_analyst = Agent(
    role="Codebase Analyst",
    goal="Analyze the structure and content of files in the codebase.",
    backstory="An expert in software architecture, skilled at understanding code and identifying areas for improvement.",
    tools=[read_file],
    llm=llm,
    verbose=True,
    allow_delegation=False
)

refactor_specialist = Agent(
    role="Code Refactoring Specialist",
    goal="Modify the codebase to add missing features or improve quality, such as adding error handling.",
    backstory="A meticulous developer who specializes in writing clean, robust, and error-free code.",
    tools=[],
    llm=llm,
    verbose=True,
    allow_delegation=False
)
EOF

# Create agents/vision_agents.py
cat << 'EOF' > "$PROJECT_DIR/agents/vision_agents.py"
import os
from crewai import Agent
from langchain_openai import ChatOpenAI
from .custom_tools import analyze_image
from dotenv import load_dotenv

load_dotenv()

lm_studio_base_url = os.getenv("LM_STUDIO_URL")
lm_studio_api_key = os.getenv("LM_STUDIO_API_KEY", "not-needed")

llm = ChatOpenAI(
    openai_api_base=lm_studio_base_url,
    openai_api_key=lm_studio_api_key,
    model_name="local-multimodal-model"
)

vision_analyst_agent = Agent(
  role='Image Analyst',
  goal='Analyze images and provide detailed descriptions and insights. If the image content is visual, describe it. If it contains text, try to extract it.',
  backstory="An expert in computer vision, capable of interpreting visual content. You are currently using a local LLM which may have limitations in directly processing images unless it's a multimodal model.",
  tools=[analyze_image],
  llm=llm,
  verbose=True,
  allow_delegation=False
)
EOF

# Create agents/security_agents.py
cat << 'EOF' > "$PROJECT_DIR/agents/security_agents.py"
import os
from crewai import Agent
from langchain_openai import ChatOpenAI
from .custom_tools import run_john_the_ripper
from dotenv import load_dotenv

load_dotenv()

lm_studio_base_url = os.getenv("LM_STUDIO_URL")
lm_studio_api_key = os.getenv("LM_STUDIO_API_KEY", "not-needed")

llm = ChatOpenAI(
    openai_api_base=lm_studio_base_url,
    openai_api_key=lm_studio_api_key,
    model_name="local-model"
)

security_auditor_agent = Agent(
    role='Security Auditor',
    goal='Perform authorized security checks on password hashes using approved tools like John the Ripper, under strict human supervision and explicit instruction.',
    backstory=(
        "A specialized security agent operating under strict protocols. "
        "You ONLY act when given explicit permission for a specific, authorized task involving pre-approved tools. "
        "You do not perform any other actions. All actions must be logged and auditable."
    ),
    tools=[run_john_the_ripper],
    llm=llm,
    allow_delegation=False,
    verbose=True
)
EOF

# Create main_agent_runner.py
cat << 'EOF' > "$PROJECT_DIR/main_agent_runner.py"
import os
from dotenv import load_dotenv

load_dotenv()

from agents.code_analysis_agents import code_analyst
# from agents.vision_agents import vision_analyst_agent
# from agents.security_agents import security_auditor_agent

from crewai import Crew, Process, Task

def run_crew():
    lm_studio_url = os.getenv("LM_STUDIO_URL")
    if not lm_studio_url:
        print("Error: LM_STUDIO_URL is not set in the .env file.")
        print("Please set it to your LM Studio server address (e.g., http://localhost:1234/v1).")
        return

    print(f"Configured to use LM Studio at: {lm_studio_url}")
    print("Initializing crew...")

    # --- Define Tasks ---
    # Example: Create src/example.py with: echo "def hello(): print('Hello from example.py')" > src/example.py
    # task1 = Task(
    #     description="Analyze the Python file named 'example.py' located in the 'src' directory.",
    #     agent=code_analyst,
    #     expected_output="A brief summary of 'example.py'."
    # )
    # my_crew = Crew(
    #     agents=[code_analyst],
    #     tasks=[task1],
    #     process=Process.sequential,
    #     verbose=2
    # )
    # print("Kicking off the crew...")
    # result = my_crew.kickoff()
    # print("\n\nCrew execution finished. Results:"); print(result)

    print("--------------------------------------------------------------------")
    print("main_agent_runner.py: No tasks defined. Edit to add tasks.")
    print("--------------------------------------------------------------------")

if __name__ == "__main__":
    run_crew()
EOF

# Create requirements.txt
cat << 'EOF' > "$PROJECT_DIR/requirements.txt"
# Core Agent Framework
crewai==0.35.8
crewai-tools==0.3.0

# Foundational Library for Agents
langchain-openai==0.1.8
langchain-community

# For Computer Vision Capabilities
opencv-python-headless

# Utilities
python-dotenv==1.0.1
EOF

# Create .env
cat << 'EOF' > "$PROJECT_DIR/.env"
# URL for your LM Studio local server.
# Example: LM_STUDIO_URL="http://YOUR_COMPUTER_IP_ADDRESS:PORT/v1"
LM_STUDIO_URL="http://192.168.1.YOUR_PC_IP:1234/v1"

# API Key for LM Studio (often not required)
LM_STUDIO_API_KEY="not-needed"
EOF

# Create Dockerfile
cat << 'EOF' > "$PROJECT_DIR/Dockerfile"
FROM python:3.11-slim

RUN apt-get update && apt-get install -y --no-install-recommends \
    libgl1-mesa-glx \
    libglib2.0-0 \
    procps \
    libgomp1 \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt
COPY . .
CMD ["python", "main_agent_runner.py"]
EOF

# Create .dockerignore
cat << 'EOF' > "$PROJECT_DIR/.dockerignore"
venv/
env/
__pycache__/
*.py[cod]
*$py.class
*.egg-info/
.Python
pip-log.txt
.tox/
.git
.vscode/
.idea/
*.DS_Store
docker-compose.override.yml
# !.env
EOF

# Create compose.yaml
cat << 'EOF' > "$PROJECT_DIR/compose.yaml"
services:
  ai_agent_system:
    build:
      context: .
      dockerfile: Dockerfile
    volumes:
      - .:/app
    environment:
      - LM_STUDIO_URL=${LM_STUDIO_URL}
      - LM_STUDIO_API_KEY=${LM_STUDIO_API_KEY}
    # network_mode: "host" # May be needed in some Docker setups to reach host services
    ports:
      - "8000:8000" # Expose port 8000 (FastAPI default via uvicorn) to the host
    restart: unless-stopped
networks:
  default:
    driver: bridge
EOF

echo "Project structure and files created in '$PROJECT_DIR'."
echo "Next, run termux_setup_part2_install.sh or follow manual setup instructions."
chmod +x "$PROJECT_DIR/main_agent_runner.py"
```
