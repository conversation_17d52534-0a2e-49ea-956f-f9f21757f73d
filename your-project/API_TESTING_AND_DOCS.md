# API Testing and Basic Documentation

This document provides example `curl` commands to test the FastAPI endpoints for the CrewAI Agent System.

## Prerequisites

1.  **LM Studio Running:** Ensure your LM Studio server is running on your PC and accessible from where you're running `curl` (e.g., your PC or Termux).
2.  **API Server Running:** The FastAPI server (`api_server.py`) must be running.
    *   **In Termux (Directly):**
        ```bash
        cd your-project
        source venv/bin/activate
        uvicorn api_server:app --host 0.0.0.0 --port 8000 --reload
        ```
        Replace `<your_phone_ip>` with your phone's actual IP address on the LAN.
    *   **Using Docker (Locally on PC or in Termux if Docker is set up):**
        ```bash
        cd your-project
        # Ensure .env has correct LM_STUDIO_URL (e.g. http://host.docker.internal:1234/v1 if LM Studio on same PC as Docker Desktop)
        # Or your PC's LAN IP if LM Studio is on PC and Docker is in Termux.
        docker compose up --build
        ```
        The API will be accessible at `http://localhost:8000` (or the host port you configured in `compose.yaml` via `API_PORT_HOST`).

3.  **`.env` file:** Ensure `your-project/.env` has the correct `LM_STUDIO_URL`.

## API Base URL

*   If running `uvicorn` directly in Termux: `export API_BASE_URL="http://<your_phone_ip>:8000"`
*   If running with `docker compose` (and default port mapping): `export API_BASE_URL="http://localhost:8000"`
    (Adjust `localhost` if your Docker setup requires a different address to reach the host).

Set this environment variable in your terminal before running `curl` commands, or replace `${API_BASE_URL}` manually.

---

## Endpoints

### 1. Root

*   **Endpoint:** `GET /`
*   **Description:** Returns a welcome message.
*   **Curl Command:**
    ```bash
    curl -X GET "${API_BASE_URL}/"
    ```
*   **Expected Response (Example):**
    ```json
    {"message":"Welcome to the CrewAI Agent System API. Visit /docs for API documentation."}
    ```

### 2. List Agents

*   **Endpoint:** `GET /agents/`
*   **Description:** Lists all registered agents and their details.
*   **Curl Command:**
    ```bash
    curl -X GET "${API_BASE_URL}/agents/"
    ```
*   **Expected Response (Example):**
    ```json
    [
        {"role":"Codebase Analyst","goal":"Analyze the structure and content of files in the codebase."},
        {"role":"Code Refactoring Specialist","goal":"Modify the codebase to add missing features or improve quality, such as adding error handling."},
        {"role":"Image Analyst","goal":"Analyze images and provide detailed descriptions and insights. If the image content is visual, describe it. If it contains text, try to extract it."},
        {"role":"Security Auditor","goal":"Perform authorized security checks on password hashes using approved tools like John the Ripper, under strict human supervision and explicit instruction."}
    ]
    ```

### 3. List Tools

*   **Endpoint:** `GET /tools/`
*   **Description:** Lists available custom tools.
*   **Curl Command:**
    ```bash
    curl -X GET "${API_BASE_URL}/tools/"
    ```
*   **Expected Response (Example - may vary based on tool docstrings):**
    ```json
    [
        {"name":"CodeFileReaderTool","description":"Reads the content of a specified file from the codebase.\nAssumes 'src' directory is at the same level as 'main_agent_runner.py'."},
        {"name":"ComputerVisionTool","description":"Analyzes an image and returns a description of its basic properties."},
        {"name":"JohnTheRipperTool","description":"Runs John the Ripper on a specified hash file for authorized security audits.\nRequires explicit user approval. This tool is for security testing ONLY."}
    ]
    ```

### 4. Submit Task

*   **Endpoint:** `POST /tasks/`
*   **Description:** Submits a task to a specified agent. Runs synchronously.
*   **Curl Command:**
    ```bash
    # Create a dummy file for the Codebase Analyst to read, if it doesn't exist
    # (Assuming your-project/src directory exists relative to where api_server.py is running)
    # mkdir -p src
    # echo "def test_function():\n  print('Hello from test_file.py')" > src/test_file.py

    curl -X POST "${API_BASE_URL}/tasks/" \
    -H "Content-Type: application/json" \
    -d '{
        "task_description": "Analyze the python file named test_file.py in the src directory. What is its content and purpose?",
        "agent_role": "Codebase Analyst"
    }'
    ```
*   **Expected Response (Example - actual result from LLM will vary):**
    ```json
    {
        "task_id": "sync_task_001",
        "status": "completed",
        "result": "The file 'test_file.py' contains a single Python function named 'test_function' which, when called, prints the string 'Hello from test_file.py' to the console. Its purpose is to serve as a simple test or example file."
    }
    ```
    **Note:** Ensure `src/test_file.py` exists where the `api_server.py` can access it (e.g., in `your-project/src/`) when testing this particular task.

*   **Example for Image Analyst (requires an image and ComputerVisionTool to be functional):**
    ```bash
    # Assuming you have an image 'sample.jpg' in the same directory as api_server.py
    # (or adjust path in task_description)
    # curl -X POST "${API_BASE_URL}/tasks/" \
    # -H "Content-Type: application/json" \
    # -d '{
    #     "task_description": "Analyze the image named sample.jpg. What are its dimensions?",
    #     "agent_role": "Image Analyst"
    # }'
    ```

### 5. Get Task Status/Result (for Synchronous Tasks)

*   **Endpoint:** `GET /tasks/{task_id}`
*   **Description:** Retrieves the status/result of the last executed synchronous task if the ID matches.
*   **Curl Command (use the ID returned by the POST /tasks/ or the default 'sync_task_001'):**
    ```bash
    curl -X GET "${API_BASE_URL}/tasks/sync_task_001"
    ```
*   **Expected Response (Example, if a task was run):**
    ```json
    {
        "task_id": "sync_task_001",
        "status": "completed",
        "result": "The file 'test_file.py' contains a single Python function named 'test_function' which, when called, prints the string 'Hello from test_file.py' to the console. Its purpose is to serve as a simple test or example file."
    }
    ```
    If no task was run yet with this ID:
    ```json
    {
        "task_id": "sync_task_001",
        "status": "pending",
        "result": "No synchronous task has been executed yet."
    }
    ```

---

## FastAPI Auto-Documentation

FastAPI provides automatic interactive API documentation. Once the server is running, you can access:

*   **Swagger UI:** `${API_BASE_URL}/docs`
*   **ReDoc:** `${API_BASE_URL}/redoc`

These interfaces allow you to explore and test the API endpoints directly from your browser.
```
