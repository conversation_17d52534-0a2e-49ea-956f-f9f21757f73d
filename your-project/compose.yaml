services:
  ai_agent_system:
    build:
      context: .
      dockerfile: Dockerfile
    volumes:
      # Mounts your project directory into the container for live updates during development.
      # This means changes to your Python code on your host machine will be reflected
      # inside the container without needing to rebuild (though a restart of the app might be needed).
      - .:/app
    environment:
      # These variables will be passed into the container.
      # They will override any values for the same variables that might be in a .env file
      # that gets COPIED into the image during the build process.
      # However, if the .env file is volume-mounted (as part of the `.:/app` mount),
      # and your Python code loads it with python-dotenv, those values will be used.
      # Best practice for Compose is to define them here if they are needed by the container at runtime.

      # The LM_STUDIO_URL should be the full URL to your LM Studio server's v1 endpoint.
      # Example: LM_STUDIO_URL=http://************:1234/v1
      # This needs to be accessible from within the Docker container.
      # If Docker is running on the same machine as LM Studio, 'localhost' might work,
      # but if on a different machine (like Termux connecting to PC), use the PC's LAN IP.
      # For Termux, ensure this IP is reachable from the Termux environment.
      - LM_STUDIO_URL=${LM_STUDIO_URL} # Value will be taken from the .env file in the project directory on the host
      - LM_STUDIO_API_KEY=${LM_STUDIO_API_KEY} # Same, from .env on host
      # API_PORT is used by uvicorn inside the container (default 8000 if not set in .env)
      # This is mostly for consistency if uvicorn command in Dockerfile were to use it.
      # The Dockerfile CMD currently hardcodes 8000.
      - API_PORT=${API_PORT:-8000}

    ports:
      # Maps host port (e.g., 8000, or API_PORT_HOST from .env if you add that variable)
      # to the container's exposed port (which is 8000 as per Dockerfile EXPOSE and CMD).
      # Example: "8001:8000" would map host port 8001 to container port 8000.
      # If API_PORT_HOST is defined in .env, use it, otherwise default host port to 8000.
      - "${API_PORT_HOST:-8000}:8000"

    # To ensure the container can connect to LM Studio on your host machine's network:
    # If LM Studio is running on the same machine as Docker, no special network_mode is usually needed.
    # If LM Studio is on your host and Docker is default (Linux), it can usually reach host ports.
    # If Docker Desktop (Mac/Win), 'host.docker.internal' can be used in LM_STUDIO_URL for localhost.
    # For Termux Docker to PC LM Studio, use PC's LAN IP.
    # network_mode: "host" # This can simplify networking by making the container share the host's network stack.
                           # Use with caution as it bypasses some container network isolation.
                           # Not always needed and might not be best practice for all scenarios.

    # The command to run, defaults to CMD in Dockerfile if not specified here.
    # command: ["python", "main_agent_runner.py"]

    # Set a TTY to true if your application is interactive in the console.
    # tty: true
    # stdin_open: true # If you need to send input to the container via `docker attach` or similar.

    restart: unless-stopped # Or 'on-failure', 'always', 'no'
    # Depends on whether you want the service to try and restart if it crashes.
networks:
  default:
    driver: bridge
