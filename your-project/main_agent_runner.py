import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# This script is now primarily for CLI testing or direct invocation if needed.
# The main way to interact with the agent system will be via the API server (api_server.py).

# Import agents and tools if you want to test them directly here.
from agents.code_analysis_agents import code_analyst
from crewai import Crew, Process, Task

def run_sample_task_cli():
    """
    Defines and runs a sample Crew via CLI for testing purposes.
    This demonstrates how you might still run agents directly if not using the API.
    """
    lm_studio_url = os.getenv("LM_STUDIO_URL")
    if not lm_studio_url:
        print("Error: LM_STUDIO_URL is not set in the .env file for CLI execution.")
        print("Please set it if you intend to run agents directly through this script.")
        # Depending on agent initialization, this might still work if agents load .env themselves.
        # return # Or proceed cautiously

    print(f"CLI Runner: Configured to use LM Studio at: {lm_studio_url} (if agents use it directly)")
    print("CLI Runner: Initializing a sample task...")

    # --- Define a Sample Task for the Code Analyst ---
    # Ensure you have a sample file for the agent to analyze, e.g., src/example_cli.py
    sample_file_path = "src/example_cli.py"
    if not os.path.exists(sample_file_path):
        print(f"CLI Runner: Warning - Sample file {sample_file_path} not found. Creating a dummy one.")
        os.makedirs("src", exist_ok=True)
        with open(sample_file_path, "w") as f:
            f.write("def hello_cli():\n    print('Hello from example_cli.py')\n")

    # Using the code_analyst agent
    # Note: The LLM for code_analyst should be configured when code_analysis_agents.py is imported,
    # typically by reading LM_STUDIO_URL from the .env file.

    if not code_analyst.llm: # or a more specific check depending on how LLM is set up
        print("CLI Runner: Error - code_analyst's LLM is not configured. Check .env and agent setup.")
        return

    task_cli = Task(
        description=f"Analyze the Python file named '{os.path.basename(sample_file_path)}' located in the 'src' directory. What is its purpose and basic structure?",
        agent=code_analyst,
        expected_output=f"A brief summary of the purpose and structure of '{os.path.basename(sample_file_path)}'."
    )

    # --- Create and Run the Crew ---
    cli_crew = Crew(
        agents=[code_analyst],
        tasks=[task_cli],
        process=Process.sequential,
        verbose=1 # Set verbosity for CLI output
    )

    print("CLI Runner: Kicking off the sample crew...")
    try:
        result = cli_crew.kickoff()
        print("\n\nCLI Runner: Crew execution finished.")
        print("CLI Results:")
        print(result)
    except Exception as e:
        print(f"CLI Runner: Error during crew execution: {e}")
        import traceback
        traceback.print_exc()

    print("--------------------------------------------------------------------")
    print("CLI Runner: Sample task execution complete.")
    print("For primary interaction, please use the API server (run: uvicorn api_server:app --reload)")
    print("--------------------------------------------------------------------")


if __name__ == "__main__":
    print("--- Running main_agent_runner.py (CLI Mode) ---")
    print("This script is intended for direct CLI testing or specific non-API workflows.")
    print("The main application interface is now through 'api_server.py'.")

    # You can uncomment this to run the sample task when script is executed directly
    # run_sample_task_cli()

    print("\nTo start the API server, run: uvicorn api_server:app --reload --port 8000 (or your chosen port)")
    print("If you want to run a sample CLI task, uncomment 'run_sample_task_cli()' in this script.")
