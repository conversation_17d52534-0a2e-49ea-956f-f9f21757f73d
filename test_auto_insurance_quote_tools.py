import unittest
from auto_insurance_quote_tools import AutoInsuranceQuote, Coverage, compare_auto_quotes

class TestAutoInsuranceQuoteTools(unittest.TestCase):

    def setUp(self):
        # Create a base quote (e.g., customer's current policy)
        self.base_policy = AutoInsuranceQuote(
            insurer_name="Current Insurer",
            premium_monthly=150.00,
            premium_annual=1800.00,
            policy_type="Full Coverage",
            coverages=[
                Coverage(name="Bodily Injury Liability", limit=100000.0),
                Coverage(name="Property Damage Liability", limit=50000.0),
                Coverage(name="Collision", deductible=500.0),
                Coverage(name="Comprehensive", deductible=250.0)
            ]
        )

        # Create some comparison quotes
        self.quote1 = AutoInsuranceQuote(
            insurer_name="Insurer A",
            premium_monthly=120.00,
            premium_annual=1440.00,
            policy_type="Full Coverage",
            coverages=[
                Coverage(name="Bodily Injury Liability", limit=100000.0),
                Coverage(name="Property Damage Liability", limit=50000.0),
                Coverage(name="Collision", deductible=500.0),
                Coverage(name="Comprehensive", deductible=250.0)
            ],
            discounts=["Good Driver", "Multi-Policy"]
        )

        self.quote2 = AutoInsuranceQuote(
            insurer_name="Insurer B",
            premium_monthly=140.00,
            premium_annual=1680.00,
            policy_type="Enhanced Coverage",
            coverages=[
                Coverage(name="Bodily Injury Liability", limit=250000.0),
                Coverage(name="Property Damage Liability", limit=100000.0),
                Coverage(name="Collision", deductible=500.0),
                Coverage(name="Comprehensive", deductible=250.0),
                Coverage(name="Roadside Assistance")
            ]
        )

        self.quote3 = AutoInsuranceQuote(
            insurer_name="Insurer C",
            premium_monthly=160.00,
            premium_annual=1920.00,
            policy_type="Basic Coverage",
            coverages=[
                Coverage(name="Bodily Injury Liability", limit=50000.0),
                Coverage(name="Property Damage Liability", limit=25000.0)
            ]
        )

        self.comparison_quotes = [self.quote1, self.quote2, self.quote3]

    def test_compare_auto_quotes(self):
        results = compare_auto_quotes(self.base_policy, self.comparison_quotes)

        # Test potential_savings_quotes
        self.assertEqual(len(results["potential_savings_quotes"]), 1)
        self.assertEqual(results["potential_savings_quotes"][0]["insurer_name"], "Insurer A")

        # Test better_coverage_quotes
        self.assertEqual(len(results["better_coverage_quotes"]), 1)
        self.assertEqual(results["better_coverage_quotes"][0]["insurer_name"], "Insurer B")

        # Test all_comparisons
        self.assertEqual(len(results["all_comparisons"]), 3)

        # Verify details for Insurer A
        insurer_a_comp = next(item for item in results["all_comparisons"] if item["insurer_name"] == "Insurer A")
        self.assertTrue(insurer_a_comp["is_cheaper_than_base"])
        self.assertFalse(insurer_a_comp["is_better_coverage_than_base"])

        # Verify details for Insurer B
        insurer_b_comp = next(item for item in results["all_comparisons"] if item["insurer_name"] == "Insurer B")
        self.assertFalse(insurer_b_comp["is_cheaper_than_base"])
        self.assertTrue(insurer_b_comp["is_better_coverage_than_base"])

        # Verify details for Insurer C
        insurer_c_comp = next(item for item in results["all_comparisons"] if item["insurer_name"] == "Insurer C")
        self.assertFalse(insurer_c_comp["is_cheaper_than_base"])
        self.assertFalse(insurer_c_comp["is_better_coverage_than_base"])

if __name__ == '__main__':
    unittest.main()
