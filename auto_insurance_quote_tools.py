import dataclasses
from typing import List, Optional

@dataclasses.dataclass
class Coverage:
    """Represents a specific type of auto insurance coverage."""
    name: str
    limit: Optional[float] = None  # e.g., $100,000
    deductible: Optional[float] = None # e.g., $500
    details: Optional[str] = None # Any specific notes about this coverage

@dataclasses.dataclass
class AutoInsuranceQuote:
    """Represents a standardized auto insurance quote."""
    insurer_name: str
    premium_monthly: float
    premium_annual: float
    policy_type: str # e.g., "Full Coverage", "Liability Only"
    coverages: List[Coverage]
    discounts: Optional[List[str]] = None
    policy_term_months: Optional[int] = None
    source_url: Optional[str] = None
    timestamp: str = dataclasses.field(default_factory=lambda: datetime.now().isoformat())

    def get_coverage_by_name(self, name: str) -> Optional[Coverage]:
        """Helper to get a specific coverage by its name."""
        for cov in self.coverages:
            if cov.name.lower() == name.lower():
                return cov
        return None

from datetime import datetime

def compare_auto_quotes(
    base_quote: AutoInsuranceQuote,
    comparison_quotes: List[AutoInsuranceQuote],
    savings_threshold_monthly: float = 20.0, # Minimum monthly savings to highlight
    better_coverage_factor: float = 1.1 # A quote is considered "better coverage" if its total limits are X times higher
) -> dict:
    """
    Compares a list of auto insurance quotes against a base quote, identifying
    options that offer savings or potentially better coverage.

    Args:
        base_quote: The customer's current or target auto insurance policy.
        comparison_quotes: A list of new quotes to compare against the base.
        savings_threshold_monthly: The minimum monthly savings (in currency) to consider a quote "better" due to price.
        better_coverage_factor: A multiplier to determine if coverage is "better".
                                For simplicity, this example uses total limits.
                                In a real system, this would be more nuanced.

    Returns:
        A dictionary containing:
        - 'potential_savings_quotes': Quotes that offer significant monthly savings.
        - 'better_coverage_quotes': Quotes that offer better coverage for similar or lower price.
        - 'all_comparisons': Detailed comparison for each quote.
    """
    results = {
        "potential_savings_quotes": [],
        "better_coverage_quotes": [],
        "all_comparisons": []
    }

    base_total_limit = sum(c.limit for c in base_quote.coverages if c.limit is not None)

    for quote in comparison_quotes:
        comparison_total_limit = sum(c.limit for c in quote.coverages if c.limit is not None)
        monthly_difference = base_quote.premium_monthly - quote.premium_monthly
        is_cheaper = monthly_difference >= savings_threshold_monthly
        is_better_coverage = comparison_total_limit >= (base_total_limit * better_coverage_factor)

        comparison_detail = {
            "insurer_name": quote.insurer_name,
            "premium_monthly": quote.premium_monthly,
            "premium_annual": quote.premium_annual,
            "monthly_difference_from_base": monthly_difference,
            "is_cheaper_than_base": is_cheaper,
            "is_better_coverage_than_base": is_better_coverage,
            "coverages": [dataclasses.asdict(c) for c in quote.coverages],
            "discounts": quote.discounts,
            "policy_type": quote.policy_type
        }
        results["all_comparisons"].append(comparison_detail)

        if is_cheaper:
            results["potential_savings_quotes"].append(dataclasses.asdict(quote))
        if is_better_coverage and monthly_difference >= -savings_threshold_monthly: # Better coverage for similar or lower price
            results["better_coverage_quotes"].append(dataclasses.asdict(quote))

    return results

# Example Usage (for testing purposes, not part of the main agent flow)
if __name__ == "__main__":
    # Create a base quote (e.g., customer's current policy)
    base_policy = AutoInsuranceQuote(
        insurer_name="Current Insurer",
        premium_monthly=150.00,
        premium_annual=1800.00,
        policy_type="Full Coverage",
        coverages=[
            Coverage(name="Bodily Injury Liability", limit=100000.0),
            Coverage(name="Property Damage Liability", limit=50000.0),
            Coverage(name="Collision", deductible=500.0),
            Coverage(name="Comprehensive", deductible=250.0)
        ]
    )

    # Create some comparison quotes
    quote1 = AutoInsuranceQuote(
        insurer_name="Insurer A",
        premium_monthly=120.00,
        premium_annual=1440.00,
        policy_type="Full Coverage",
        coverages=[
            Coverage(name="Bodily Injury Liability", limit=100000.0),
            Coverage(name="Property Damage Liability", limit=50000.0),
            Coverage(name="Collision", deductible=500.0),
            Coverage(name="Comprehensive", deductible=250.0)
        ],
        discounts=["Good Driver", "Multi-Policy"]
    )

    quote2 = AutoInsuranceQuote(
        insurer_name="Insurer B",
        premium_monthly=140.00,
        premium_annual=1680.00,
        policy_type="Enhanced Coverage",
        coverages=[
            Coverage(name="Bodily Injury Liability", limit=250000.0),
            Coverage(name="Property Damage Liability", limit=100000.0),
            Coverage(name="Collision", deductible=500.0),
            Coverage(name="Comprehensive", deductible=250.0),
            Coverage(name="Roadside Assistance")
        ]
    )

    quote3 = AutoInsuranceQuote(
        insurer_name="Insurer C",
        premium_monthly=160.00,
        premium_annual=1920.00,
        policy_type="Basic Coverage",
        coverages=[
            Coverage(name="Bodily Injury Liability", limit=50000.0),
            Coverage(name="Property Damage Liability", limit=25000.0)
        ]
    )

    # Compare the quotes
    comparison_results = compare_auto_quotes(base_policy, [quote1, quote2, quote3])

    print("--- Comparison Results ---")
    print("Potential Savings Quotes:")
    for q in comparison_results["potential_savings_quotes"]:
        print(f"- {q['insurer_name']}: ${q['premium_monthly']:.2f}/month")

    print("\nBetter Coverage Quotes (for similar or lower price):")
    for q in comparison_results["better_coverage_quotes"]:
        print(f"- {q['insurer_name']}: ${q['premium_monthly']:.2f}/month (Total Limit: {sum(c['limit'] for c in q['coverages'] if c['limit'] is not None):.2f})")

    print("\nAll Comparisons:")
    for comp in comparison_results["all_comparisons"]:
        print(f"- Insurer: {comp['insurer_name']}, Monthly: ${comp['premium_monthly']:.2f}, Diff: ${comp['monthly_difference_from_base']:.2f}, Cheaper: {comp['is_cheaper_than_base']}, Better Coverage: {comp['is_better_coverage_than_base']}")
