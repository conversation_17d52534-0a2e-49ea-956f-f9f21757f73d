import dataclasses
from typing import List, Optional
from playwright.sync_api import sync_playwright
import time
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Import AutoInsuranceQuote and Coverage from auto_insurance_quote_tools
# Assuming auto_insurance_quote_tools.py is in the same directory
from auto_insurance_quote_tools import AutoInsuranceQuote, Coverage

# Define a generic Quote class for Life/Health (can be refined later)
@dataclasses.dataclass
class GenericQuote:
    insurer_name: str
    premium_monthly: Optional[float] = None
    premium_annual: Optional[float] = None
    policy_type: str
    details: Optional[str] = None
    source_url: Optional[str] = None
    timestamp: str = dataclasses.field(default_factory=lambda: datetime.now().isoformat())

class InsuranceQuoteScraper:
    def __init__(self):
        # Client information for Alyssa <PERSON> Chirinos (for auto)
        self.auto_client_info = {
            "first_name": "<PERSON><PERSON>",
            "last_name": "<PERSON><PERSON><PERSON>",
            "dob": "08/16/1997",
            "address": "4300 18th st w",
            "apt_unit": "I-204",
            "city": "Bradenton",
            "state": "FL",
            "zip_code": "34205",
            "phone": "**********",
            "email": "<EMAIL>",
            "vehicle_year": "2018",
            "vehicle_make": "Hyundai",
            "vehicle_model": "Elantra",
            "vehicle_color": "Blue",
            "current_insurer": "State Farm",
            "current_monthly_premium": 280.00,
            # SSN and DLIC are NOT included here due to security and privacy concerns.
            # These would need to be handled securely and manually if required by the site.
        }

        # Generic client info for Life/Health (placeholder)
        # This data will be used for Policygenius and HealthSherpa PoCs
        self.generic_client_info = {
            "first_name": "Alyssa Mariah",
            "last_name": "Chirinos",
            "dob": "08/16/1997", # MM/DD/YYYY
            "gender": "female",
            "zip_code": "34205",
            "tobacco": "no",
            "health_status": "excellent",
            "coverage_amount": "500000", # As a string for form input
            "term_length": "20", # As a string for form input
            "household_size": "1", # For health insurance
            "income": "50000", # For health insurance
        }

    # --- Auto Insurance Scraping Functions (from previous iteration) ---
    def _extract_geico_quote(self, page) -> Optional[AutoInsuranceQuote]:
        logging.info("Attempting to extract quote from GEICO page...")
        try:
            premium_text = page.locator("div.total-premium-amount").inner_text()
            premium = float(premium_text.replace("$", "").replace(",", ""))
            logging.info(f"Found GEICO premium: {premium}")
            coverages = [
                Coverage(name="Bodily Injury Liability", limit=50000.0),
                Coverage(name="Property Damage Liability", limit=25000.0),
                Coverage(name="Collision", deductible=500.0),
                Coverage(name="Comprehensive", deductible=250.0)
            ]
            return AutoInsuranceQuote(
                insurer_name="GEICO",
                premium_monthly=premium,
                premium_annual=premium * 12,
                policy_type="Standard",
                coverages=coverages,
                source_url=page.url,
                timestamp=datetime.now().isoformat()
            )
        except Exception as e:
            logging.error(f"Could not extract GEICO quote: {e}")
            return None

    def scrape_geico(self) -> Optional[AutoInsuranceQuote]:
        logging.info("Starting GEICO scraping for Alyssa Mariah Chirinos...")
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=True) 
            page = browser.new_page()
            try:
                page.goto("https://www.geico.com/auto-insurance/quote/")
                logging.info(f"Navigated to GEICO: {page.url}")
                # --- Placeholder for GEICO form filling logic ---
                # You need to manually go through the GEICO quoting process and identify each step.
                # Example (selectors are illustrative and likely incorrect):
                # page.fill("#zipCode", self.auto_client_info["zip_code"])
                # page.click("#startQuoteButton") # Example button
                # page.wait_for_load_state("networkidle") # Wait for page to load
                # ... (more form filling for personal and vehicle info)
                time.sleep(10) 
                quote = self._extract_geico_quote(page)
                return quote
            except Exception as e:
                logging.error(f"Error during GEICO scraping: {e}")
                return None
            finally:
                browser.close()

    def _extract_progressive_quote(self, page) -> Optional[AutoInsuranceQuote]:
        logging.info("Attempting to extract quote from Progressive page...")
        try:
            premium_text = page.locator("span.total-premium").inner_text()
            premium = float(premium_text.replace("$", "").replace(",", ""))
            logging.info(f"Found Progressive premium: {premium}")
            coverages = [
                Coverage(name="Liability", limit=100000.0),
                Coverage(name="Collision", deductible=1000.0)
            ]
            return AutoInsuranceQuote(
                insurer_name="Progressive",
                premium_monthly=premium,
                premium_annual=premium * 12,
                policy_type="Standard",
                coverages=coverages,
                source_url=page.url,
                timestamp=datetime.now().isoformat()
            )
        except Exception as e:
            logging.error(f"Could not extract Progressive quote: {e}")
            return None

    def scrape_progressive(self) -> Optional[AutoInsuranceQuote]:
        logging.info("Starting Progressive scraping...")
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=True)
            page = browser.new_page()
            try:
                page.goto("https://www.progressive.com/auto/quotes/")
                logging.info(f"Navigated to Progressive: {page.url}")
                # --- Placeholder for Progressive form filling logic ---
                # Similar to GEICO, this will be highly specific.
                # page.fill("#zipCode_overlay", self.auto_client_info["zip_code"])
                # page.click("#qsButton_overlay")
                # time.sleep(2)
                # ... and so on
                time.sleep(5) 
                quote = self._extract_progressive_quote(page)
                return quote
            except Exception as e:
                logging.error(f"Error during Progressive scraping: {e}")
                return None
            finally:
                browser.close()

    # --- Life Insurance Scraping Functions ---
    def _extract_policygenius_life_quote(self, page) -> Optional[GenericQuote]:
        logging.info("Attempting to extract quote from Policygenius page...")
        try:
            # --- IMPORTANT: These selectors are examples and WILL need to be updated ---
            # Inspect Policygenius results page for actual premium and details.
            premium_text = page.locator("span.premium-amount").first.inner_text()
            premium = float(premium_text.replace("$", "").replace(",", ""))
            policy_type = page.locator("h3.policy-type").first.inner_text()
            details = page.locator("div.policy-details").first.inner_text()

            return GenericQuote(
                insurer_name="Policygenius (Life)",
                premium_monthly=premium,
                premium_annual=premium * 12,
                policy_type=policy_type,
                details=details,
                source_url=page.url,
                timestamp=datetime.now().isoformat()
            )
        except Exception as e:
            logging.error(f"Could not extract Policygenius life quote: {e}")
            return None

    def scrape_policygenius_life(self) -> Optional[GenericQuote]:
        logging.info("Starting Policygenius life insurance scraping...")
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=True)
            page = browser.new_page()
            try:
                page.goto("https://www.policygenius.com/life-insurance/")
                logging.info(f"Navigated to Policygenius: {page.url}")

                # --- Placeholder for Policygenius form filling logic ---
                # This will involve filling out DOB, gender, zip, tobacco, health, coverage amount, term length.
                # Example (selectors are illustrative and likely incorrect):
                # page.fill("#dob", self.generic_client_info["dob"])
                # page.select_option("#gender", self.generic_client_info["gender"])
                # page.fill("#zipCode", self.generic_client_info["zip_code"])
                # page.click("#getQuoteButton")
                # page.wait_for_load_state("networkidle")

                time.sleep(15) # Arbitrary wait, replace with proper waits

                quote = self._extract_policygenius_life_quote(page)
                return quote

            except Exception as e:
                logging.error(f"Error during Policygenius life scraping: {e}")
                return None
            finally:
                browser.close()

    # --- Health Insurance Scraping Functions ---
    def _extract_healthsherpa_health_quote(self, page) -> Optional[GenericQuote]:
        logging.info("Attempting to extract quote from HealthSherpa page...")
        try:
            # --- IMPORTANT: These selectors are examples and WILL need to be updated ---
            # Inspect HealthSherpa results page for actual premium and details.
            premium_text = page.locator("div.plan-premium").first.inner_text()
            premium = float(premium_text.replace("$", "").replace(",", ""))
            plan_name = page.locator("h2.plan-name").first.inner_text()
            details = page.locator("div.plan-summary").first.inner_text()

            return GenericQuote(
                insurer_name="HealthSherpa (Health)",
                premium_monthly=premium,
                premium_annual=premium * 12,
                policy_type=plan_name,
                details=details,
                source_url=page.url,
                timestamp=datetime.now().isoformat()
            )
        except Exception as e:
            logging.error(f"Could not extract HealthSherpa health quote: {e}")
            return None

    def scrape_healthsherpa_health(self) -> Optional[GenericQuote]:
        logging.info("Starting HealthSherpa health insurance scraping...")
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=True)
            page = browser.new_page()
            try:
                page.goto("https://www.healthsherpa.com/")
                logging.info(f"Navigated to HealthSherpa: {page.url}")

                # --- Placeholder for HealthSherpa form filling logic ---
                # This will involve filling out zip code, household size, income, etc.
                # Example (selectors are illustrative and likely incorrect):
                # page.fill("#zip", self.generic_client_info["zip_code"])
                # page.click("#startButton")
                # page.wait_for_load_state("networkidle")

                time.sleep(15) # Arbitrary wait, replace with proper waits

                quote = self._extract_healthsherpa_health_quote(page)
                return quote

            except Exception as e:
                logging.error(f"Error during HealthSherpa health scraping: {e}")
                return None
            finally:
                browser.close()

# Example Usage (for testing purposes)
if __name__ == "__main__":
    scraper = InsuranceQuoteScraper()

    # --- Auto Insurance Example ---
    # print("\n--- Auto Insurance (GEICO) ---")
    # geico_quote = scraper.scrape_geico()
    # if geico_quote:
    #     print(f"GEICO Quote: ${geico_quote.premium_monthly:.2f} / month")
    # else:
    #     print("Failed to get GEICO quote.")

    # --- Life Insurance Example ---
    print("\n--- Life Insurance (Policygenius) ---")
    policygenius_quote = scraper.scrape_policygenius_life()
    if policygenius_quote:
        print(f"Policygenius Life Quote: ${policygenius_quote.premium_monthly:.2f} / month")
        print(f"Policy Type: {policygenius_quote.policy_type}")
    else:
        print("Failed to get Policygenius life quote.")

    # --- Health Insurance Example ---
    print("\n--- Health Insurance (HealthSherpa) ---")
    healthsherpa_quote = scraper.scrape_healthsherpa_health()
    if healthsherpa_quote:
        print(f"HealthSherpa Health Quote: ${healthsherpa_quote.premium_monthly:.2f} / month")
        print(f"Plan Name: {healthsherpa_quote.policy_type}")
    else:
        print("Failed to get HealthSherpa health quote.")
